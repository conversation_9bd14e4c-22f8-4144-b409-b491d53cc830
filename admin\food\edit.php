<?php
/**
 * Admin Food Management - Edit
 */
require_once '../../app/config/config.php';
require_once '../../app/config/database.php';
require_once '../../app/helpers.php';
require_once '../../app/models/FoodItem.php';
require_once '../../app/models/Category.php';

// Check if user is admin
if (!is_admin()) {
    set_error_message('Bạn không có quyền truy cập trang này');
    redirect(BASE_URL);
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_error_message('ID món ăn không hợp lệ');
    redirect(ADMIN_URL . '/food/index.php');
}

$foodId = (int)$_GET['id'];

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Initialize models
$foodModel = new FoodItem($conn);
$categoryModel = new Category($conn);

// Get food item
$food = $foodModel->getById($foodId);

if (!$food) {
    set_error_message('Không tìm thấy món ăn');
    redirect(ADMIN_URL . '/food/index.php');
}

// Get all active categories
$categories = $categoryModel->getActiveCategories();

// Initialize errors array
$errors = [];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && verify_csrf_token($_POST['csrf_token'] ?? '')) {
    // Validate form data
    $updatedFood = [
        'food_name' => sanitize($_POST['food_name'] ?? ''),
        'category_id' => isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0,
        'price' => isset($_POST['price']) ? (float)$_POST['price'] : 0,
        'description' => sanitize($_POST['description'] ?? ''),
        'status' => isset($_POST['status']) ? (int)$_POST['status'] : 0
    ];
    
    // Validate food name
    if (empty($updatedFood['food_name'])) {
        $errors['food_name'] = 'Vui lòng nhập tên món ăn';
    }
    
    // Validate category
    if (empty($updatedFood['category_id'])) {
        $errors['category_id'] = 'Vui lòng chọn danh mục';
    } else {
        $category = $categoryModel->getById($updatedFood['category_id']);
        if (!$category) {
            $errors['category_id'] = 'Danh mục không tồn tại';
        }
    }
    
    // Validate price
    if ($updatedFood['price'] <= 0) {
        $errors['price'] = 'Giá phải lớn hơn 0';
    }
    
    // Process image upload
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 2 * 1024 * 1024; // 2MB
        
        if (!in_array($_FILES['image']['type'], $allowedTypes)) {
            $errors['image'] = 'Chỉ chấp nhận file ảnh (JPEG, PNG, GIF, WEBP)';
        } elseif ($_FILES['image']['size'] > $maxSize) {
            $errors['image'] = 'Kích thước file không được vượt quá 2MB';
        } else {
            // Create upload directory if it doesn't exist
            $uploadDir = ROOT_DIR . '/public/uploads/food/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            
            // Generate unique filename
            $fileExt = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
            $fileName = 'food_' . time() . '_' . random_string(8) . '.' . $fileExt;
            $uploadPath = $uploadDir . $fileName;
            
            if (move_uploaded_file($_FILES['image']['tmp_name'], $uploadPath)) {
                // Delete old image if exists
                if (!empty($food['image_path']) && file_exists(ROOT_DIR . '/public/' . $food['image_path'])) {
                    unlink(ROOT_DIR . '/public/' . $food['image_path']);
                }
                
                $updatedFood['image_path'] = 'uploads/food/' . $fileName;
            } else {
                $errors['image'] = 'Không thể tải lên file ảnh';
            }
        }
    } else {
        // Keep existing image
        $updatedFood['image_path'] = $food['image_path'];
    }
    
    // If no errors, update in database
    if (empty($errors)) {
        if ($foodModel->update($foodId, $updatedFood)) {
            set_success_message('Món ăn đã được cập nhật thành công');
            redirect(ADMIN_URL . '/food/index.php');
        } else {
            set_error_message('Có lỗi xảy ra khi cập nhật món ăn');
        }
    } else {
        // If there are errors, update the food variable for form re-display
        $food = array_merge($food, $updatedFood);
    }
}

// Set page title
$pageTitle = 'Chỉnh sửa món ăn';

// Set extra styles for image preview
$extraStyles = '<style>
    .image-preview {
        width: 200px;
        height: 200px;
        object-fit: cover;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
</style>';

// Set extra scripts for image preview
$extraScripts = '<script>
    document.addEventListener("DOMContentLoaded", function() {
        const imageInput = document.getElementById("image");
        const imagePreview = document.getElementById("imagePreview");

        imageInput.addEventListener("change", function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreview.className = "food-img-large food-img-real mb-3";
                }

                reader.readAsDataURL(this.files[0]);
            }
        });
    });
</script>';

// Include content in layout
$content = VIEWS_DIR . '/admin/food/edit.php';
include_once VIEWS_DIR . '/layouts/main.php';