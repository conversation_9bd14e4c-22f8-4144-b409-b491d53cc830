<?php
/**
 * Staff Payments Management
 */
require_once '../../app/config/config.php';
require_once '../../app/config/database.php';
require_once '../../app/helpers.php';
require_once '../../app/models/Order.php';
require_once '../../app/models/Table.php';

// Check if user is staff
if (!is_staff()) {
    set_error_message('Bạn không có quyền truy cập trang này');
    redirect(BASE_URL);
}

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Initialize models
$orderModel = new Order($conn);
$tableModel = new Table($conn);

// Get current user
$currentUser = get_logged_in_user();

// Get completed orders that need payment
$completedOrders = $orderModel->query(
    "SELECT o.*, t.table_number, p.payment_id, p.payment_status
     FROM orders o 
     JOIN tables t ON o.table_id = t.table_id 
     LEFT JOIN payments p ON o.order_id = p.order_id 
     WHERE o.status = 'completed' AND o.user_id = :user_id
     ORDER BY o.order_date DESC",
    [':user_id' => $currentUser['user_id']]
);

// Get today's payments by this staff
$todayPayments = $orderModel->query(
    "SELECT p.*, o.order_id, t.table_number
     FROM payments p
     JOIN orders o ON p.order_id = o.order_id
     JOIN tables t ON o.table_id = t.table_id
     WHERE p.received_by = :user_id AND DATE(p.payment_date) = :today
     ORDER BY p.payment_date DESC",
    [':user_id' => $currentUser['user_id'], ':today' => date('Y-m-d')]
);

// Calculate today's revenue
$todayRevenue = array_reduce($todayPayments, function($total, $payment) {
    return $total + ($payment['payment_status'] === 'completed' ? $payment['payment_amount'] : 0);
}, 0);

// Set page title
$pageTitle = 'Quản lý thanh toán';

// Include content in layout
$content = VIEWS_DIR . '/staff/payments/index.php';
include_once VIEWS_DIR . '/layouts/main.php';
