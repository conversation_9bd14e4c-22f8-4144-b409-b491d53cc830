<?php
/**
 * Base Model Class
 * Provides common database operations for all models
 */
class BaseModel {
    protected $conn;
    protected $table;
    protected $primaryKey = 'id';

    /**
     * Constructor
     *
     * @param PDO $db Database connection
     * @param string $table Table name
     */
    public function __construct($db, $table) {
        $this->conn = $db;
        $this->table = $table;
    }

    /**
     * Get all records from table
     *
     * @param string $orderBy Order by clause
     * @param int $limit Limit results
     * @param int $offset Offset for pagination
     * @return array Records
     */
    public function getAll($orderBy = null, $limit = null, $offset = null) {
        $sql = "SELECT * FROM {$this->table}";

        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }

        $params = [];

        if ($limit) {
            $sql .= " LIMIT :limit";
            $params[':limit'] = $limit;

            if ($offset) {
                $sql .= " OFFSET :offset";
                $params[':offset'] = $offset;
            }
        }

        $stmt = $this->conn->prepare($sql);

        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value, PDO::PARAM_INT);
        }

        $stmt->execute();

        return $stmt->fetchAll();
    }
    
    /**
     * Count all records in table
     *
     * @param string $conditions WHERE conditions
     * @param array $params Parameters for conditions
     * @return int Count of records
     */
    public function count($conditions = '', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        
        if ($conditions) {
            $sql .= " WHERE {$conditions}";
        }
        
        $stmt = $this->conn->prepare($sql);
        
        if ($params) {
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
        }
        
        $stmt->execute();
        $result = $stmt->fetch();
        
        return $result['count'];
    }

    /**
     * Get record by ID
     *
     * @param int $id Record ID
     * @return array|null Record or null if not found
     */
    public function getById($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id LIMIT 1";
        $stmt = $this->conn->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            return $stmt->fetch();
        }
        
        return null;
    }

    /**
     * Find records by conditions
     *
     * @param string $conditions WHERE conditions
     * @param array $params Parameters for conditions
     * @param string $orderBy Order by clause
     * @param int $limit Limit results
     * @param int $offset Offset for pagination
     * @return array Records
     */
    public function findBy($conditions, $params = [], $orderBy = null, $limit = null, $offset = null) {
        $sql = "SELECT * FROM {$this->table} WHERE {$conditions}";

        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }

        if ($limit) {
            $sql .= " LIMIT :limit";
            $params[':limit'] = $limit;

            if ($offset) {
                $sql .= " OFFSET :offset";
                $params[':offset'] = $offset;
            }
        }

        $stmt = $this->conn->prepare($sql);

        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * Find one record by conditions
     *
     * @param string $conditions WHERE conditions
     * @param array $params Parameters for conditions
     * @return array|null Record or null if not found
     */
    public function findOneBy($conditions, $params = []) {
        $sql = "SELECT * FROM {$this->table} WHERE {$conditions} LIMIT 1";
        $stmt = $this->conn->prepare($sql);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            return $stmt->fetch();
        }
        
        return null;
    }

    /**
     * Create a new record
     *
     * @param array $data Record data
     * @return int|bool Last insert ID or false on failure
     */
    public function create($data) {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$this->table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->conn->prepare($sql);
        
        foreach ($data as $key => $value) {
            $stmt->bindValue(":{$key}", $value);
        }
        
        if ($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        
        return false;
    }

    /**
     * Update a record
     *
     * @param int $id Record ID
     * @param array $data Record data
     * @return bool True on success, false on failure
     */
    public function update($id, $data) {
        $fields = [];
        
        foreach (array_keys($data) as $key) {
            $fields[] = "{$key} = :{$key}";
        }
        
        $fieldsStr = implode(', ', $fields);
        
        $sql = "UPDATE {$this->table} SET {$fieldsStr} WHERE {$this->primaryKey} = :id";
        $stmt = $this->conn->prepare($sql);
        
        $stmt->bindValue(':id', $id);
        
        foreach ($data as $key => $value) {
            $stmt->bindValue(":{$key}", $value);
        }
        
        return $stmt->execute();
    }

    /**
     * Delete a record
     *
     * @param int $id Record ID
     * @return bool True on success, false on failure
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = :id";
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':id', $id);
        
        return $stmt->execute();
    }

    /**
     * Execute custom query
     *
     * @param string $sql SQL query
     * @param array $params Parameters for query
     * @return array|bool Query results or false on failure
     */
    public function query($sql, $params = []) {
        $stmt = $this->conn->prepare($sql);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->execute();
        
        return $stmt->fetchAll();
    }

    /**
     * Begin a transaction
     *
     * @return bool True on success, false on failure
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    /**
     * Commit a transaction
     *
     * @return bool True on success, false on failure
     */
    public function commit() {
        return $this->conn->commit();
    }

    /**
     * Rollback a transaction
     *
     * @return bool True on success, false on failure
     */
    public function rollback() {
        return $this->conn->rollBack();
    }
}