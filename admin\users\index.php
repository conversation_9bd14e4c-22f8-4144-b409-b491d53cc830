<?php
/**
 * Admin User Management - List
 */
require_once '../../app/config/config.php';
require_once '../../app/config/database.php';
require_once '../../app/helpers.php';
require_once '../../app/models/User.php';

// Check if user is admin
if (!is_admin()) {
    set_error_message('Bạn không có quyền truy cập trang này');
    redirect(BASE_URL);
}

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Initialize models
$userModel = new User($conn);

// Handle search query
$search = '';
$roleFilter = '';
if (isset($_GET['search'])) {
    $search = sanitize($_GET['search']);
}
if (isset($_GET['role'])) {
    $roleFilter = sanitize($_GET['role']);
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Initialize variables to prevent undefined variable errors
$users = [];
$totalItems = 0;

// Get users with filters
$hasSearch = !empty($search) && trim($search) !== '';
$hasRoleFilter = !empty($roleFilter) && trim($roleFilter) !== '' && $roleFilter !== '0';

if ($hasSearch || $hasRoleFilter) {
    // Build search conditions
    $conditions = [];
    $params = [];

    if ($hasSearch) {
        $conditions[] = "(u.username LIKE :search)";
        $params[':search'] = "%{$search}%";
    }

    if ($hasRoleFilter) {
        $conditions[] = "u.role_id = :role_id";
        $params[':role_id'] = $roleFilter;
    }

    // Build the WHERE clause
    $whereClause = 'WHERE ' . implode(' AND ', $conditions);

    // Main query with search filters
    $sql = "SELECT u.*, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            {$whereClause}
            ORDER BY u.created_at DESC
            LIMIT {$perPage} OFFSET {$offset}";

    try {
        // Debug logging
        error_log("Search Debug - SQL: " . $sql);
        error_log("Search Debug - Params: " . print_r($params, true));
        error_log("Search Debug - Search term: '$search'");
        error_log("Search Debug - Role filter: '$roleFilter'");

        $stmt = $conn->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
            error_log("Search Debug - Binding $key = '$value'");
        }
        $stmt->execute();
        $users = $stmt->fetchAll();
        error_log("Search Debug - Found " . count($users) . " users");

        // Count query for pagination
        $countSql = "SELECT COUNT(*) as count
                     FROM users u
                     JOIN roles r ON u.role_id = r.role_id
                     {$whereClause}";
        error_log("Search Debug - Count SQL: " . $countSql);

        $countStmt = $conn->prepare($countSql);
        foreach ($params as $key => $value) {
            $countStmt->bindValue($key, $value);
        }
        $countStmt->execute();
        $totalItems = $countStmt->fetch()['count'];
        error_log("Search Debug - Total items: " . $totalItems);

    } catch (Exception $e) {
        // Log detailed error information
        error_log("Search Error Details:");
        error_log("- Message: " . $e->getMessage());
        error_log("- File: " . $e->getFile());
        error_log("- Line: " . $e->getLine());
        error_log("- SQL: " . $sql);
        error_log("- Params: " . print_r($params, true));
        error_log("- Search: '$search'");
        error_log("- Role: '$roleFilter'");

        set_error_message('Có lỗi xảy ra khi tìm kiếm. Hiển thị tất cả người dùng.');
        $users = $userModel->getAllWithRoles('u.created_at DESC', $perPage, $offset);
        $totalItems = $userModel->count();
    }
} else {
    // No filters - show all users
    $users = $userModel->getAllWithRoles('u.created_at DESC', $perPage, $offset);
    $totalItems = $userModel->count();
}

// Ensure users is always an array
if (!is_array($users)) {
    $users = [];
}

// Calculate total pages
$totalPages = ceil($totalItems / $perPage);

// Handle delete action if confirmed
if (isset($_POST['delete']) && isset($_POST['user_id']) && verify_csrf_token($_POST['csrf_token'] ?? '')) {
    $userId = (int)$_POST['user_id'];
    $user = $userModel->getById($userId);
    
    if ($user) {
        // Prevent deleting current admin user
        $currentUser = get_logged_in_user();
        if ($userId == $currentUser['user_id']) {
            set_error_message('Bạn không thể xóa tài khoản của chính mình');
        } else {
            if ($userModel->delete($userId)) {
                set_success_message('Người dùng đã được xóa thành công');
            } else {
                set_error_message('Không thể xóa người dùng');
            }
        }
    } else {
        set_error_message('Người dùng không tồn tại');
    }
    
    redirect(ADMIN_URL . '/users/index.php');
}

// Get roles for filter dropdown
$sql = "SELECT * FROM roles ORDER BY role_name";
$stmt = $conn->prepare($sql);
$stmt->execute();
$roles = $stmt->fetchAll();

// Set page title
$pageTitle = 'Quản lý người dùng';

// Include content in layout
$content = VIEWS_DIR . '/admin/users/index.php';
include_once VIEWS_DIR . '/layouts/main.php';
