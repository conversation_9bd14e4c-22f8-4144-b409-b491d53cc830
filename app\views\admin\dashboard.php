<div class="container-fluid py-4">
    <h1 class="mb-4">Bảng điều khiển quản trị</h1>
    
    <!-- Stats Overview -->
    <div class="row mb-4">
        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Tổng người dùng</h5>
                            <h2 class="mb-0 fw-bold"><?= $totalUsers ?></h2>
                        </div>
                        <div class="bg-primary rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-users fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Món ăn</h5>
                            <h2 class="mb-0 fw-bold"><?= $totalCategories ?></h2>
                        </div>
                        <div class="bg-success rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-tags fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Món ăn</h5>
                            <h2 class="mb-0 fw-bold"><?= $totalFoodItems ?></h2>
                        </div>
                        <div class="bg-warning rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-utensils fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Số bàn</h5>
                            <h2 class="mb-0 fw-bold"><?= $totalTables ?></h2>
                        </div>
                        <div class="bg-danger rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-chair fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Table Status -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Trạng thái bàn</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Trạng thái</th>
                                    <th>Số lượng</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <span class="badge bg-success">Trống</span> Bàn trống
                                    </td>
                                    <td><?= $tableStatus['available'] ?? 0 ?></td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge bg-danger">Đang sử dụng</span> Đang phục vụ
                                    </td>
                                    <td><?= $tableStatus['occupied'] ?? 0 ?></td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge bg-warning">Đã đặt</span> Đã đặt trước
                                    </td>
                                    <td><?= $tableStatus['reserved'] ?? 0 ?></td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">Bảo trì</span> Đang bảo trì
                                    </td>
                                    <td><?= $tableStatus['maintenance'] ?? 0 ?></td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr class="fw-bold">
                                    <td>Tổng số bàn</td>
                                    <td><?= $totalTables ?></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <a href="<?= ADMIN_URL ?>/tables/index.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-chair me-1"></i> Quản lý bàn
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Daily Revenue -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Doanh thu hôm nay</h5>
                </div>
                <div class="card-body d-flex flex-column justify-content-center">
                    <div class="text-center">
                        <h2 class="display-4 fw-bold text-primary mb-3">
                            <?= format_currency($dailyRevenue['total_revenue'] ?? 0) ?>
                        </h2>
                        <p class="mb-0">Số đơn hàng: <strong><?= $dailyRevenue['order_count'] ?? 0 ?></strong></p>
                        <p class="text-muted mb-0">Ngày: <?= date('d/m/Y') ?></p>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <a href="<?= ADMIN_URL ?>/reports/daily.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-chart-bar me-1"></i> Xem báo cáo chi tiết
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Recent Orders -->
        <div class="col-lg-7 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Đơn hàng gần đây</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Bàn</th>
                                    <th>Nhân viên</th>
                                    <th>Tổng tiền</th>
                                    <th>Trạng thái</th>
                                    <th>Thời gian</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recentOrders)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center">Không có đơn hàng nào</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($recentOrders as $order): ?>
                                        <tr>
                                            <td><?= $order['order_id'] ?></td>
                                            <td><?= $order['table_number'] ?></td>
                                            <td><?= $order['staff_name'] ?></td>
                                            <td><?= format_currency($order['final_amount']) ?></td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusText = '';
                                                
                                                switch ($order['status']) {
                                                    case 'pending':
                                                        $statusClass = 'bg-warning';
                                                        $statusText = 'Đang chờ';
                                                        break;
                                                    case 'processing':
                                                        $statusClass = 'bg-info';
                                                        $statusText = 'Đang xử lý';
                                                        break;
                                                    case 'completed':
                                                        $statusClass = 'bg-success';
                                                        $statusText = 'Hoàn thành';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'bg-danger';
                                                        $statusText = 'Đã hủy';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                            </td>
                                            <td><?= date('d/m/Y H:i', strtotime($order['order_date'])) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <a href="<?= ADMIN_URL ?>/orders/index.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-list me-1"></i> Xem tất cả đơn hàng
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Popular Items -->
        <div class="col-lg-5 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Món ăn phổ biến</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Món ăn</th>
                                    <th>Danh mục</th>
                                    <th>Số lượng</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($popularItems)): ?>
                                    <tr>
                                        <td colspan="3" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($popularItems as $item): ?>
                                        <tr>
                                            <td><?= $item['food_name'] ?></td>
                                            <td><?= $item['category_name'] ?></td>
                                            <td><?= $item['total_quantity'] ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <a href="<?= ADMIN_URL ?>/reports/popular.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-chart-pie me-1"></i> Xem báo cáo chi tiết
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>