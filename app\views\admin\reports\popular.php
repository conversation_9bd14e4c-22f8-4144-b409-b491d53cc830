<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Báo cáo món ăn bán ch<PERSON></h1>
        <div class="d-flex gap-2">
            <a href="<?= ADMIN_URL ?>/reports/daily.php" class="btn btn-outline-primary">
                <i class="fas fa-calendar-day me-1"></i> Báo cáo ngày
            </a>
            <a href="<?= ADMIN_URL ?>/reports/weekly.php" class="btn btn-outline-primary">
                <i class="fas fa-calendar-week me-1"></i> Báo cáo tuần
            </a>
        </div>
    </div>

    <!-- Date Range and Category Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Từ ngày</label>
                    <input type="date" id="start_date" name="start_date" class="form-control" value="<?= $startDate ?>" max="<?= date('Y-m-d') ?>">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">Đến ngày</label>
                    <input type="date" id="end_date" name="end_date" class="form-control" value="<?= $endDate ?>" max="<?= date('Y-m-d') ?>">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">Danh mục</label>
                    <select name="category" id="category" class="form-select">
                        <option value="0">Tất cả danh mục</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?= $category['category_id'] ?>" <?= $categoryFilter == $category['category_id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($category['category_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i> Xem báo cáo
                    </button>
                    <a href="<?= ADMIN_URL ?>/reports/popular.php" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh me-1"></i> Đặt lại
                    </a>
                </div>
            </form>
            <div class="mt-2">
                <small class="text-muted">
                    Báo cáo từ <strong><?= date('d/m/Y', strtotime($startDate)) ?></strong> 
                    đến <strong><?= date('d/m/Y', strtotime($endDate)) ?></strong>
                    (<?= $daysDiff ?> ngày)
                </small>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= number_format($totalItemsSold) ?></h4>
                            <p class="mb-0">Tổng món đã bán</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-utensils fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= format_currency($totalRevenue) ?></h4>
                            <p class="mb-0">Tổng doanh thu</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= $totalUniqueItems ?></h4>
                            <p class="mb-0">Món khác nhau</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= round($totalItemsSold / $daysDiff, 1) ?></h4>
                            <p class="mb-0">Món TB/ngày</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sales by Hour Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Lượng bán theo giờ trong ngày</h5>
                </div>
                <div class="card-body">
                    <canvas id="hourlyChart" height="100"></canvas>
                </div>
            </div>

    <!-- Popular Items List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Danh sách món ăn bán chạy</h5>
        </div>
        <div class="card-body">
            <?php if (empty($popularItems)): ?>
                <div class="alert alert-info">
                    Không có dữ liệu món ăn trong khoảng thời gian đã chọn.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Hạng</th>
                                <th>Hình ảnh</th>
                                <th>Tên món</th>
                                <th>Danh mục</th>
                                <th>Giá</th>
                                <th>Số lượng bán</th>
                                <th>Số đơn hàng</th>
                                <th>SL TB/đơn</th>
                                <th>Doanh thu</th>
                                <th>% Tổng DT</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($popularItems as $index => $item): ?>
                                <tr>
                                    <td>
                                        <?php if ($index < 3): ?>
                                            <span class="badge bg-warning fs-6"><?= $index + 1 ?></span>
                                        <?php elseif ($index < 10): ?>
                                            <span class="badge bg-info"><?= $index + 1 ?></span>
                                        <?php else: ?>
                                            <?= $index + 1 ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php $imageData = get_food_image($item['image_path'], 'small'); ?>
                                        <img src="<?= $imageData['src'] ?>"
                                             alt="<?= htmlspecialchars($item['food_name']) ?>"
                                             class="<?= $imageData['class'] ?> rounded">
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars($item['food_name']) ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= htmlspecialchars($item['category_name']) ?></span>
                                    </td>
                                    <td><?= format_currency($item['price']) ?></td>
                                    <td>
                                        <strong class="text-primary"><?= number_format($item['total_quantity']) ?></strong>
                                    </td>
                                    <td><?= number_format($item['order_count']) ?></td>
                                    <td><?= round($item['avg_quantity_per_order'], 1) ?></td>
                                    <td>
                                        <strong class="text-success"><?= format_currency($item['total_revenue']) ?></strong>
                                    </td>
                                    <td>
                                        <?= $totalRevenue > 0 ? round(($item['total_revenue'] / $totalRevenue) * 100, 1) : 0 ?>%
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr class="table-primary">
                                <th colspan="5">Tổng cộng</th>
                                <th><?= number_format($totalItemsSold) ?></th>
                                <th>-</th>
                                <th>-</th>
                                <th><?= format_currency($totalRevenue) ?></th>
                                <th>100%</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Top 3 Items Cards -->
                <?php if (count($popularItems) >= 3): ?>
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="mb-3">Top 3 món bán chạy nhất</h6>
                        </div>
                        <?php for ($i = 0; $i < min(3, count($popularItems)); $i++): ?>
                            <?php $item = $popularItems[$i]; ?>
                            <div class="col-md-4 mb-3">
                                <div class="card border-<?= $i == 0 ? 'warning' : ($i == 1 ? 'info' : 'success') ?>">
                                    <div class="card-body text-center">
                                        <div class="position-relative mb-3">
                                            <?php if (!empty($item['image_path'])): ?>
                                                <img src="<?= BASE_URL ?>/public/<?= $item['image_path'] ?>"
                                                     alt="<?= htmlspecialchars($item['food_name']) ?>"
                                                     class="rounded-circle"
                                                     style="width: 80px; height: 80px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto"
                                                     style="width: 80px; height: 80px;">
                                                    <i class="fas fa-utensils fa-2x text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-<?= $i == 0 ? 'warning' : ($i == 1 ? 'info' : 'success') ?>">
                                                <?= $i + 1 ?>
                                            </span>
                                        </div>
                                        <h6 class="card-title"><?= htmlspecialchars($item['food_name']) ?></h6>
                                        <p class="card-text">
                                            <small class="text-muted"><?= htmlspecialchars($item['category_name']) ?></small><br>
                                            <strong class="text-primary"><?= number_format($item['total_quantity']) ?> món đã bán</strong><br>
                                            <strong class="text-success"><?= format_currency($item['total_revenue']) ?></strong>
                                        </p>
                                        <div class="progress">
                                            <div class="progress-bar bg-<?= $i == 0 ? 'warning' : ($i == 1 ? 'info' : 'success') ?>"
                                                 style="width: <?= $totalItemsSold > 0 ? ($item['total_quantity'] / $popularItems[0]['total_quantity']) * 100 : 0 ?>%">
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            <?= $totalItemsSold > 0 ? round(($item['total_quantity'] / $totalItemsSold) * 100, 1) : 0 ?>% tổng lượng bán
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Hourly Sales Chart
const ctx = document.getElementById('hourlyChart').getContext('2d');
const hourlyChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: <?= json_encode(array_map(function($h) { return $h . ':00'; }, range(0, 23))) ?>,
        datasets: [{
            label: 'Số lượng món bán',
            data: <?= json_encode(array_column($hourlyChart, 'total_quantity')) ?>,
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            x: {
                title: {
                    display: true,
                    text: 'Giờ trong ngày'
                }
            },
            y: {
                title: {
                    display: true,
                    text: 'Số lượng món bán'
                },
                beginAtZero: true
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return 'Số lượng: ' + context.parsed.y.toLocaleString('vi-VN') + ' món';
                    }
                }
            }
        }
    }
});
</script>
        </div>

        <!-- Category Breakdown -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Phân tích theo danh mục</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($categoryBreakdown as $category): ?>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span><?= htmlspecialchars($category['category_name']) ?></span>
                                <span class="badge bg-primary"><?= $category['total_quantity'] ?></span>
                            </div>
                            <div class="progress mb-1">
                                <div class="progress-bar" style="width: <?= $totalItemsSold > 0 ? ($category['total_quantity'] / $totalItemsSold) * 100 : 0 ?>%"></div>
                            </div>
                            <small class="text-muted">
                                <?= format_currency($category['total_revenue']) ?> 
                                (<?= $category['unique_items'] ?> món khác nhau)
                            </small>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
