<?php
/**
 * Staff Orders List
 */
require_once '../../app/config/config.php';
require_once '../../app/config/database.php';
require_once '../../app/helpers.php';
require_once '../../app/models/Order.php';
require_once '../../app/models/Table.php';

// Check if user is staff
if (!is_staff()) {
    set_error_message('Bạn không có quyền truy cập trang này');
    redirect(BASE_URL);
}

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Initialize models
$orderModel = new Order($conn);
$tableModel = new Table($conn);

// Get current user
$currentUser = get_logged_in_user();

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Filter parameters
$status = $_GET['status'] ?? '';
$date = $_GET['date'] ?? '';

// Build conditions
$conditions = 'user_id = :user_id';
$params = [':user_id' => $currentUser['user_id']];

if ($status) {
    $conditions .= ' AND status = :status';
    $params[':status'] = $status;
}

if ($date) {
    $conditions .= ' AND DATE(order_date) = :date';
    $params[':date'] = $date;
}

// Get orders
$orders = $orderModel->findBy($conditions, $params, 'order_date DESC', $limit, $offset);

// Get total count for pagination
$totalOrders = $orderModel->count($conditions, $params);
$totalPages = ceil($totalOrders / $limit);

// Get orders with details
$ordersWithDetails = [];
foreach ($orders as $order) {
    $table = $tableModel->getById($order['table_id']);
    $order['table_number'] = $table['table_number'];
    $ordersWithDetails[] = $order;
}

// Set page title
$pageTitle = 'Quản lý đơn hàng';

// Include content in layout
$content = VIEWS_DIR . '/staff/orders/index.php';
include_once VIEWS_DIR . '/layouts/main.php';
