<?php
require_once __DIR__ . '/../../../helpers.php';
if (!is_staff()) {
    set_error_message('<PERSON>ạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/login.php');
}
require_once __DIR__ . '/../../../models/Order.php';
require_once __DIR__ . '/../../../config/database.php';
$orderModel = new Order($conn);
$user = get_logged_in_user();
$selectedDate = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$dailyRevenue = $orderModel->getDailyRevenueByUser($user['user_id'], $selectedDate);
$orders = $orderModel->getOrdersByUserAndDate($user['user_id'], $selectedDate);
include __DIR__ . '/../../layouts/header.php';
?>
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Báo cáo doanh thu cá nhân theo ngày</h1>
        <form method="GET" action="" class="d-flex gap-2">
            <input type="date" name="date" class="form-control" value="<?= $selectedDate ?>" max="<?= date('Y-m-d') ?>">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search me-1"></i> Xem báo cáo
            </button>
        </form>
    </div>
    <div class="row mb-4">
        <div class="col-lg-4 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h4><?= format_currency($dailyRevenue['total_revenue'] ?? 0) ?></h4>
                    <p class="mb-0">Tổng doanh thu</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h4><?= $dailyRevenue['order_count'] ?? 0 ?></h4>
                    <p class="mb-0">Tổng đơn hàng</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h4><?= format_currency($dailyRevenue['average_order_value'] ?? 0) ?></h4>
                    <p class="mb-0">Giá trị TB/đơn</p>
                </div>
            </div>
        </div>
    </div>
    <div class="card">
        <div class="card-body">
            <h5 class="mb-3">Danh sách đơn hàng</h5>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Mã đơn</th>
                            <th>Bàn</th>
                            <th>Thời gian</th>
                            <th>Thành tiền</th>
                            <th>Trạng thái</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td><?= $order['order_id'] ?></td>
                                <td><?= htmlspecialchars($order['table_number']) ?></td>
                                <td><?= date('H:i', strtotime($order['order_date'])) ?></td>
                                <td><?= format_currency($order['final_amount']) ?></td>
                                <td>
                                    <?php
                                    switch($order['status']) {
                                        case 'completed':
                                            echo '<span class="badge bg-success">Hoàn thành</span>';
                                            break;
                                        case 'pending':
                                            echo '<span class="badge bg-warning">Đang phục vụ</span>';
                                            break;
                                        case 'cancelled':
                                            echo '<span class="badge bg-danger">Đã huỷ</span>';
                                            break;
                                    }
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php include __DIR__ . '/../../layouts/footer.php'; ?>