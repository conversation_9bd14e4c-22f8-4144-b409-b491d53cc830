<?php
require_once __DIR__ . '/../../../helpers.php';
if (!is_staff()) {
    set_error_message('Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/login.php');
}
require_once __DIR__ . '/../../../models/Order.php';
require_once __DIR__ . '/../../../models/Table.php';
require_once __DIR__ . '/../../../models/FoodItem.php';
require_once __DIR__ . '/../../../config/database.php';
$orderModel = new Order($conn);
$tableModel = new Table($conn);
$foodModel = new FoodItem($conn);
$tableId = isset($_GET['table_id']) ? (int)$_GET['table_id'] : 0;
$table = $tableModel->getById($tableId);
$order = $orderModel->getTableWithActiveOrder($tableId);
if (!$table || !$order) {
    set_error_message('Không tìm thấy bàn hoặc đơn hàng đang phục vụ.');
    redirect(STAFF_URL . '/dashboard.php');
}
$orderDetails = $orderModel->getOrderDetails($order['order_id']);
$foodItems = $foodModel->getAllWithCategories();
include __DIR__ . '/../../layouts/header.php';
?>
<div class="container py-4">
    <h1 class="mb-4">Đơn hàng cho bàn <?= htmlspecialchars($table['table_number']) ?></h1>
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">Chi tiết đơn hàng</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Tên món</th>
                            <th>Số lượng</th>
                            <th>Đơn giá</th>
                            <th>Thành tiền</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orderDetails as $detail): ?>
                            <tr>
                                <td><?= htmlspecialchars($detail['food_name']) ?></td>
                                <td><?= $detail['quantity'] ?></td>
                                <td><?= format_currency($detail['unit_price']) ?></td>
                                <td><?= format_currency($detail['subtotal']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="row mb-3">
                <div class="col-md-4">
                    <label class="form-label">Tổng cộng:</label>
                    <div class="fw-bold" id="order-total">
                        <?= format_currency($order['total_amount']) ?>
                    </div>
                </div>
                <div class="col-md-4">
                    <form method="POST" action="<?= STAFF_URL ?>/orders/discount.php" class="d-flex align-items-end">
                        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                        <input type="hidden" name="order_id" value="<?= $order['order_id'] ?>">
                        <label for="discount" class="form-label me-2">Giảm giá (%)</label>
                        <input type="number" name="discount" id="discount" min="0" max="100" value="<?= $order['discount_percent'] ?>" class="form-control form-control-sm me-2" style="width: 80px;">
                        <button type="submit" class="btn btn-warning btn-sm">
                            <i class="fas fa-percent"></i> Áp dụng
                        </button>
                    </form>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Thành tiền sau giảm:</label>
                    <div class="fw-bold" id="order-final">
                        <?= format_currency($order['final_amount']) ?>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <form method="POST" action="<?= STAFF_URL ?>/orders/transfer.php" class="row g-2 align-items-end">
                    <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                    <input type="hidden" name="order_id" value="<?= $order['order_id'] ?>">
                    <div class="col-md-6">
                        <label for="to_table_id" class="form-label">Chuyển sang bàn</label>
                        <select name="to_table_id" id="to_table_id" class="form-select">
                            <?php foreach ($tableModel->getAll() as $t): ?>
                                <?php if ($t['table_id'] != $table['table_id'] && $t['status'] == 'available'): ?>
                                    <option value="<?= $t['table_id'] ?>">Bàn <?= htmlspecialchars($t['table_number']) ?></option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="text" name="reason" class="form-control" placeholder="Lý do chuyển bàn (tuỳ chọn)">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-random"></i> Chuyển bàn
                        </button>
                    </div>
                </form>
            </div>
            <div class="d-flex gap-2">
                <form method="POST" action="<?= STAFF_URL ?>/orders/complete.php" onsubmit="return confirm('Xác nhận thanh toán và hoàn thành đơn hàng?');">
                    <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                    <input type="hidden" name="order_id" value="<?= $order['order_id'] ?>">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Thanh toán & Hoàn thành
                    </button>
                </form>
                <form method="POST" action="<?= STAFF_URL ?>/orders/cancel.php" onsubmit="return confirm('Bạn có chắc muốn huỷ đơn hàng này?');">
                    <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                    <input type="hidden" name="order_id" value="<?= $order['order_id'] ?>">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i> Huỷ đơn
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php include __DIR__ . '/../../layouts/footer.php'; ?>