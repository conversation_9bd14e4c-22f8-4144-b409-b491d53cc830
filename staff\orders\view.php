<?php
/**
 * Staff View Order
 */
require_once '../../app/config/config.php';
require_once '../../app/config/database.php';
require_once '../../app/helpers.php';
require_once '../../app/models/Order.php';
require_once '../../app/models/Table.php';

// Check if user is staff
if (!is_staff()) {
    set_error_message('Bạn không có quyền truy cập trang này');
    redirect(BASE_URL);
}

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Initialize models
$orderModel = new Order($conn);
$tableModel = new Table($conn);

// Get current user
$currentUser = get_logged_in_user();

// Get order ID from URL
$orderId = (int)($_GET['id'] ?? 0);

if (!$orderId) {
    set_error_message('Không tìm thấy đơn hàng');
    redirect(STAFF_URL . '/orders/index.php');
}

// Get order details
$order = $orderModel->getOrderWithDetails($orderId);

if (!$order || $order['user_id'] != $currentUser['user_id']) {
    set_error_message('Bạn không có quyền truy cập đơn hàng này');
    redirect(STAFF_URL . '/orders/index.php');
}

// Handle order status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    // Verify CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        set_error_message('Token bảo mật không hợp lệ');
    } else {
        $action = $_POST['action'];

        switch ($action) {
            case 'start_processing':
                if ($orderModel->update($orderId, ['status' => 'processing'])) {
                    set_success_message('Đã chuyển đơn hàng sang trạng thái đang xử lý');
                } else {
                    set_error_message('Có lỗi xảy ra khi cập nhật trạng thái đơn hàng');
                }
                break;

            case 'complete_order':
                if ($orderModel->completeOrder($orderId)) {
                    set_success_message('Đã hoàn thành đơn hàng');
                } else {
                    set_error_message('Có lỗi xảy ra khi hoàn thành đơn hàng');
                }
                break;

            case 'cancel_order':
                if ($orderModel->cancelOrder($orderId)) {
                    set_success_message('Đã hủy đơn hàng');
                } else {
                    set_error_message('Có lỗi xảy ra khi hủy đơn hàng');
                }
                break;
        }

        redirect(STAFF_URL . '/orders/view.php?id=' . $orderId);
    }
}

// Refresh order data after potential updates
$order = $orderModel->getOrderWithDetails($orderId);

// Check if payment exists
$payment = $orderModel->query(
    "SELECT * FROM payments WHERE order_id = :order_id",
    [':order_id' => $orderId]
);

$hasPayment = !empty($payment);
$paymentInfo = $hasPayment ? $payment[0] : null;

// Set page title
$pageTitle = 'Chi tiết đơn hàng #' . $orderId;

// Include content in layout
$content = VIEWS_DIR . '/staff/orders/view.php';
include_once VIEWS_DIR . '/layouts/main.php';
