<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Quản lý món ăn</h1>
        <a href="<?= ADMIN_URL ?>/food/create.php" class="btn btn-primary">
            <i class="fas fa-plus-circle me-2"></i> Thêm món ăn mới
        </a>
    </div>
    
    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="" method="GET" class="row g-3">
                <div class="col-md-6">
                    <div class="form-outline">
                        <input type="text" id="search" name="search" class="form-control" value="<?= htmlspecialchars($search) ?>">
                        <label class="form-label" for="search">Tìm kiếm món ăn</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <select class="form-select" name="category_id" id="category_id">
                        <option value="">-- Tất cả danh mục --</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?= $category['category_id'] ?>" <?= $categoryId == $category['category_id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($category['category_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i> Tìm kiếm
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Food Items List -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($foodItems)): ?>
                <div class="alert alert-info">
                    Không tìm thấy món ăn nào. <a href="<?= ADMIN_URL ?>/food/create.php">Thêm món ăn mới</a>.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Hình ảnh</th>
                                <th>Tên món ăn</th>
                                <th>Danh mục</th>
                                <th>Giá</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($foodItems as $item): ?>
                                <tr>
                                    <td><?= $item['food_id'] ?></td>
                                    <td>
                                        <?php $imageData = get_food_image($item['image_path'], 'small'); ?>
                                        <img src="<?= $imageData['src'] ?>"
                                             alt="<?= htmlspecialchars($item['food_name']) ?>"
                                             class="<?= $imageData['class'] ?> img-thumbnail">
                                    </td>
                                    <td><?= htmlspecialchars($item['food_name']) ?></td>
                                    <td><?= htmlspecialchars($item['category_name']) ?></td>
                                    <td><?= format_currency($item['price']) ?></td>
                                    <td>
                                        <?php if ($item['status'] == 1): ?>
                                            <span class="badge bg-success">Còn món</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Hết món</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= ADMIN_URL ?>/food/edit.php?id=<?= $item['food_id'] ?>" 
                                               class="btn btn-warning" title="Sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger" 
                                                    data-mdb-toggle="modal" 
                                                    data-mdb-target="#deleteModal<?= $item['food_id'] ?>" 
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        
                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal<?= $item['food_id'] ?>" tabindex="-1" 
                                             aria-labelledby="deleteModalLabel<?= $item['food_id'] ?>" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel<?= $item['food_id'] ?>">
                                                            Xác nhận xóa
                                                        </h5>
                                                        <button type="button" class="btn-close" 
                                                                data-mdb-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        Bạn có chắc chắn muốn xóa món ăn "<?= htmlspecialchars($item['food_name']) ?>" không?
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" 
                                                                data-mdb-dismiss="modal">Hủy</button>
                                                        <form action="" method="POST">
                                                            <input type="hidden" name="food_id" value="<?= $item['food_id'] ?>">
                                                            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                                                            <button type="submit" name="delete" class="btn btn-danger">Xóa</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center mt-4">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($categoryId) ? '&category_id=' . $categoryId : '' ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($categoryId) ? '&category_id=' . $categoryId : '' ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($categoryId) ? '&category_id=' . $categoryId : '' ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>