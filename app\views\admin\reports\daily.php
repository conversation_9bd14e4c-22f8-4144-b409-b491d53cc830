<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>B<PERSON>o c<PERSON>o doanh thu theo ngày</h1>
        <div class="d-flex gap-2">
            <a href="<?= ADMIN_URL ?>/reports/weekly.php" class="btn btn-outline-primary">
                <i class="fas fa-calendar-week me-1"></i> Báo cáo tuần
            </a>
            <a href="<?= ADMIN_URL ?>/reports/monthly.php" class="btn btn-outline-primary">
                <i class="fas fa-calendar-alt me-1"></i> Báo cáo tháng
            </a>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="date" class="form-label">Chọn ngày</label>
                    <input type="date" id="date" name="date" class="form-control" value="<?= $selectedDate ?>" max="<?= date('Y-m-d') ?>">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i> Xem báo cáo
                    </button>
                </div>
                <div class="col-md-7 text-end">
                    <span class="text-muted">Báo cáo cho ngày: <strong><?= date('d/m/Y', strtotime($selectedDate)) ?></strong></span>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= format_currency($dailyRevenue['total_revenue'] ?? 0) ?></h4>
                            <p class="mb-0">Tổng doanh thu</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= $totalOrders ?></h4>
                            <p class="mb-0">Tổng đơn hàng</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-receipt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= format_currency($averageOrderValue) ?></h4>
                            <p class="mb-0">Giá trị TB/đơn</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= $completedOrders ?></h4>
                            <p class="mb-0">Đơn hoàn thành</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Hourly Revenue Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Doanh thu theo giờ</h5>
                </div>
                <div class="card-body">
                    <canvas id="hourlyChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Order Status Breakdown -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Trạng thái đơn hàng</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Hoàn thành</span>
                            <span class="badge bg-success"><?= $completedOrders ?></span>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-success" style="width: <?= $totalOrders > 0 ? ($completedOrders / $totalOrders) * 100 : 0 ?>%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Đang xử lý</span>
                            <span class="badge bg-warning"><?= $pendingOrders ?></span>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-warning" style="width: <?= $totalOrders > 0 ? ($pendingOrders / $totalOrders) * 100 : 0 ?>%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Đã hủy</span>
                            <span class="badge bg-danger"><?= $cancelledOrders ?></span>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-danger" style="width: <?= $totalOrders > 0 ? ($cancelledOrders / $totalOrders) * 100 : 0 ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods -->
    <?php if (!empty($paymentMethods)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Phương thức thanh toán</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($paymentMethods as $method): ?>
                            <div class="col-md-3 mb-3">
                                <div class="text-center">
                                    <h4><?= format_currency($method['total']) ?></h4>
                                    <p class="mb-0">
                                        <?php
                                        switch($method['payment_method']) {
                                            case 'cash': echo 'Tiền mặt'; break;
                                            case 'credit_card': echo 'Thẻ tín dụng'; break;
                                            case 'debit_card': echo 'Thẻ ghi nợ'; break;
                                            case 'mobile_payment': echo 'Thanh toán di động'; break;
                                            default: echo ucfirst($method['payment_method']);
                                        }
                                        ?>
                                    </p>
                                    <small class="text-muted"><?= $method['count'] ?> giao dịch</small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Detailed Orders -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Chi tiết đơn hàng</h5>
        </div>
        <div class="card-body">
            <?php if (empty($orders)): ?>
                <div class="alert alert-info">
                    Không có đơn hàng nào trong ngày <?= date('d/m/Y', strtotime($selectedDate)) ?>.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Thời gian</th>
                                <th>Bàn</th>
                                <th>Nhân viên</th>
                                <th>Tổng tiền</th>
                                <th>Thanh toán</th>
                                <th>Trạng thái</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order): ?>
                                <tr>
                                    <td><?= $order['order_id'] ?></td>
                                    <td><?= date('H:i', strtotime($order['order_date'])) ?></td>
                                    <td><?= htmlspecialchars($order['table_number']) ?></td>
                                    <td><?= htmlspecialchars($order['staff_name']) ?></td>
                                    <td><?= format_currency($order['final_amount']) ?></td>
                                    <td>
                                        <?php if ($order['payment_amount']): ?>
                                            <?= format_currency($order['payment_amount']) ?>
                                            <br><small class="text-muted">
                                                <?php
                                                switch($order['payment_method']) {
                                                    case 'cash': echo 'Tiền mặt'; break;
                                                    case 'credit_card': echo 'Thẻ tín dụng'; break;
                                                    case 'debit_card': echo 'Thẻ ghi nợ'; break;
                                                    case 'mobile_payment': echo 'Di động'; break;
                                                    default: echo ucfirst($order['payment_method']);
                                                }
                                                ?>
                                            </small>
                                        <?php else: ?>
                                            <span class="text-muted">Chưa thanh toán</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = '';
                                        $statusText = '';
                                        switch($order['status']) {
                                            case 'completed':
                                                $statusClass = 'bg-success';
                                                $statusText = 'Hoàn thành';
                                                break;
                                            case 'pending':
                                                $statusClass = 'bg-warning';
                                                $statusText = 'Chờ xử lý';
                                                break;
                                            case 'processing':
                                                $statusClass = 'bg-info';
                                                $statusText = 'Đang xử lý';
                                                break;
                                            case 'cancelled':
                                                $statusClass = 'bg-danger';
                                                $statusText = 'Đã hủy';
                                                break;
                                        }
                                        ?>
                                        <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Hourly Revenue Chart
const ctx = document.getElementById('hourlyChart').getContext('2d');
const hourlyChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?= json_encode(array_map(function($h) { return $h . ':00'; }, range(0, 23))) ?>,
        datasets: [{
            label: 'Doanh thu (VND)',
            data: <?= json_encode(array_column($chartData, 'revenue')) ?>,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'Số đơn hàng',
            data: <?= json_encode(array_column($chartData, 'order_count')) ?>,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            yAxisID: 'y1',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Doanh thu (VND)'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Số đơn hàng'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});
</script>
