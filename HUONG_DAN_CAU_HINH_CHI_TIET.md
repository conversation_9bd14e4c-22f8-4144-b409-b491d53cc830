# Hướng Dẫn C<PERSON><PERSON> Chi Tiết - <PERSON>ệ Thống Quản Lý Quán Cafe

## Mục <PERSON>
1. [<PERSON><PERSON>u <PERSON>ình <PERSON>ôi Trường Development](#cấu-hình-môi-trường-development)
2. [<PERSON><PERSON><PERSON> Môi Trường Production](#cấu-hình-môi-trường-production)
3. [<PERSON><PERSON><PERSON>](#cấu-hình-bảo-mật)
4. [Cấu Hình Performance](#cấu-hình-performance)
5. [Backup và Restore](#backup-và-restore)
6. [Monitoring và Logging](#monitoring-và-logging)

## Cấu Hình Môi Trường Development

### 1. Cấu Hình PHP Development

Tạo file `php.ini` hoặc chỉnh sửa file hiện có:

```ini
; Hiển thị lỗi cho development
display_errors = On
display_startup_errors = On
error_reporting = E_ALL

; Tăng memory limit
memory_limit = 256M

; T<PERSON><PERSON> thời gian thực thi
max_execution_time = 300

; <PERSON><PERSON><PERSON> hình upload file
file_uploads = On
upload_max_filesize = 10M
post_max_size = 12M
max_file_uploads = 20

; Session configuration
session.gc_maxlifetime = 3600
session.cookie_lifetime = 0
session.cookie_secure = 0
session.cookie_httponly = 1

; Timezone
date.timezone = "Asia/Ho_Chi_Minh"
```

### 2. Cấu Hình Apache Development

Tạo file `.htaccess` trong thư mục gốc:

```apache
# Bật rewrite engine
RewriteEngine On

# Chuyển hướng HTTP sang HTTPS (tùy chọn)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Bảo vệ file cấu hình
<Files "*.php">
    <RequireAll>
        Require all denied
        Require local
    </RequireAll>
</Files>

# Bảo vệ thư mục app
<Directory "app/">
    Require all denied
</Directory>

# Cho phép truy cập file tĩnh
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
    Require all granted
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

### 3. Cấu Hình MySQL Development

```sql
-- Tạo user riêng cho development
CREATE USER 'dev_user'@'localhost' IDENTIFIED BY 'dev_password';
GRANT ALL PRIVILEGES ON restaurant_management.* TO 'dev_user'@'localhost';

-- Cấu hình MySQL cho development
SET GLOBAL sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';
SET GLOBAL innodb_buffer_pool_size = 128M;
```

## Cấu Hình Môi Trường Production

### 1. Cấu Hình PHP Production

```ini
; Tắt hiển thị lỗi
display_errors = Off
display_startup_errors = Off
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
log_errors = On
error_log = /var/log/php/error.log

; Bảo mật
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Performance
memory_limit = 512M
max_execution_time = 60
max_input_time = 60

; OPcache
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1

; Session bảo mật
session.cookie_secure = 1
session.cookie_httponly = 1
session.use_strict_mode = 1
session.cookie_samesite = "Strict"
```

### 2. Cấu Hình Apache Production

```apache
# Virtual Host cho production
<VirtualHost *:443>
    ServerName yourdomain.com
    DocumentRoot /var/www/html/restaurant
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # Security Headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
    
    # Hide server information
    ServerTokens Prod
    ServerSignature Off
    
    # Logging
    ErrorLog ${APACHE_LOG_DIR}/restaurant_error.log
    CustomLog ${APACHE_LOG_DIR}/restaurant_access.log combined
    
    <Directory "/var/www/html/restaurant">
        AllowOverride All
        Require all granted
        
        # Disable directory browsing
        Options -Indexes
        
        # Protect sensitive files
        <FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
            Require all denied
        </FilesMatch>
    </Directory>
</VirtualHost>
```

### 3. Cấu Hình Database Production

```sql
-- Tạo user với quyền hạn chế
CREATE USER 'prod_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT SELECT, INSERT, UPDATE, DELETE ON restaurant_management.* TO 'prod_user'@'localhost';

-- Cấu hình MySQL cho production
SET GLOBAL innodb_buffer_pool_size = 1G;
SET GLOBAL query_cache_size = 64M;
SET GLOBAL slow_query_log = 1;
SET GLOBAL long_query_time = 2;
```

## Cấu Hình Bảo Mật

### 1. Mã Hóa Mật Khẩu

Cập nhật file `app/helpers.php` để sử dụng mã hóa mạnh:

```php
function hash_password($password) {
    return password_hash($password, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3
    ]);
}

function verify_password($password, $hash) {
    return password_verify($password, $hash);
}
```

### 2. CSRF Protection

Thêm CSRF token vào forms:

```php
// Tạo CSRF token
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Xác thực CSRF token
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}
```

### 3. Input Validation

```php
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validate_phone($phone) {
    return preg_match('/^[0-9]{10,11}$/', $phone);
}
```

## Cấu Hình Performance

### 1. Database Optimization

```sql
-- Tạo indexes cho performance
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_payments_date ON payments(payment_date);
CREATE INDEX idx_users_role ON users(role_id);

-- Optimize tables
OPTIMIZE TABLE orders;
OPTIMIZE TABLE order_details;
OPTIMIZE TABLE payments;
OPTIMIZE TABLE users;
```

### 2. Caching Strategy

Tạo file `app/cache.php`:

```php
<?php
class SimpleCache {
    private $cache_dir;
    
    public function __construct($cache_dir = 'cache/') {
        $this->cache_dir = $cache_dir;
        if (!is_dir($this->cache_dir)) {
            mkdir($this->cache_dir, 0755, true);
        }
    }
    
    public function get($key) {
        $file = $this->cache_dir . md5($key) . '.cache';
        if (file_exists($file) && (time() - filemtime($file)) < 3600) {
            return unserialize(file_get_contents($file));
        }
        return false;
    }
    
    public function set($key, $data) {
        $file = $this->cache_dir . md5($key) . '.cache';
        file_put_contents($file, serialize($data));
    }
    
    public function delete($key) {
        $file = $this->cache_dir . md5($key) . '.cache';
        if (file_exists($file)) {
            unlink($file);
        }
    }
}
```

## Backup và Restore

### 1. Script Backup Database

Tạo file `scripts/backup.php`:

```php
<?php
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'restaurant_management';

$backup_file = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
$command = "mysqldump --host={$host} --user={$username} --password={$password} {$database} > {$backup_file}";

exec($command, $output, $return_var);

if ($return_var === 0) {
    echo "Backup thành công: {$backup_file}\n";
} else {
    echo "Backup thất bại!\n";
}
?>
```

### 2. Script Backup Files

```bash
#!/bin/bash
# backup_files.sh

DATE=$(date +%Y-%m-%d_%H-%M-%S)
BACKUP_DIR="/backup/restaurant_$DATE"

# Tạo thư mục backup
mkdir -p $BACKUP_DIR

# Backup files
cp -r /var/www/html/restaurant $BACKUP_DIR/
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR/

echo "File backup completed: $BACKUP_DIR.tar.gz"
```

### 3. Automated Backup với Cron

```bash
# Thêm vào crontab
crontab -e

# Backup hàng ngày lúc 2:00 AM
0 2 * * * /path/to/backup_script.sh

# Backup database mỗi 6 giờ
0 */6 * * * php /path/to/backup.php
```

## Monitoring và Logging

### 1. Error Logging

Tạo file `app/logger.php`:

```php
<?php
class Logger {
    private $log_file;
    
    public function __construct($log_file = 'logs/app.log') {
        $this->log_file = $log_file;
        $dir = dirname($log_file);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    public function log($level, $message, $context = []) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] {$level}: {$message}";
        
        if (!empty($context)) {
            $log_entry .= ' ' . json_encode($context);
        }
        
        file_put_contents($this->log_file, $log_entry . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
    
    public function error($message, $context = []) {
        $this->log('ERROR', $message, $context);
    }
    
    public function info($message, $context = []) {
        $this->log('INFO', $message, $context);
    }
    
    public function warning($message, $context = []) {
        $this->log('WARNING', $message, $context);
    }
}
```

### 2. Performance Monitoring

```php
// Thêm vào đầu file
$start_time = microtime(true);
$start_memory = memory_get_usage();

// Thêm vào cuối file
$end_time = microtime(true);
$end_memory = memory_get_usage();

$execution_time = ($end_time - $start_time) * 1000; // ms
$memory_used = ($end_memory - $start_memory) / 1024; // KB

if ($execution_time > 1000) { // Log nếu > 1 giây
    $logger = new Logger();
    $logger->warning('Slow page load', [
        'page' => $_SERVER['REQUEST_URI'],
        'time' => $execution_time . 'ms',
        'memory' => $memory_used . 'KB'
    ]);
}
```

### 3. Health Check Script

Tạo file `health_check.php`:

```php
<?php
header('Content-Type: application/json');

$health = [
    'status' => 'ok',
    'timestamp' => date('c'),
    'checks' => []
];

// Kiểm tra database
try {
    require_once 'app/config/database.php';
    $db = new Database();
    $conn = $db->getConnection();
    $health['checks']['database'] = 'ok';
} catch (Exception $e) {
    $health['status'] = 'error';
    $health['checks']['database'] = 'error';
}

// Kiểm tra disk space
$free_space = disk_free_space('.');
$total_space = disk_total_space('.');
$usage_percent = (($total_space - $free_space) / $total_space) * 100;

if ($usage_percent > 90) {
    $health['status'] = 'warning';
    $health['checks']['disk_space'] = 'warning';
} else {
    $health['checks']['disk_space'] = 'ok';
}

// Kiểm tra thư mục uploads
if (is_writable('public/uploads/')) {
    $health['checks']['uploads'] = 'ok';
} else {
    $health['status'] = 'error';
    $health['checks']['uploads'] = 'error';
}

echo json_encode($health, JSON_PRETTY_PRINT);
?>
```

---

**Lưu ý:** Các cấu hình trên cần được điều chỉnh theo môi trường cụ thể của bạn. Luôn test kỹ trước khi áp dụng vào production.
