<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="mb-0"><PERSON>h toán đơn hàng</h1>
            <p class="text-muted">X<PERSON> lý thanh toán cho đơn hàng #<?= $order['order_id'] ?></p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= STAFF_URL ?>/payments/index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Order Summary -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Thông tin đơn hàng</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Mã đơn hàng:</strong></div>
                        <div class="col-sm-8">#<?= $order['order_id'] ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Bàn:</strong></div>
                        <div class="col-sm-8"><?= $order['table_number'] ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Nhân viên:</strong></div>
                        <div class="col-sm-8"><?= $order['staff_name'] ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Thời gian:</strong></div>
                        <div class="col-sm-8"><?= date('d/m/Y H:i', strtotime($order['order_date'])) ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Trạng thái:</strong></div>
                        <div class="col-sm-8">
                            <span class="badge bg-success">Hoàn thành</span>
                        </div>
                    </div>
                    
                    <?php if ($order['notes']): ?>
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Ghi chú:</strong></div>
                            <div class="col-sm-8"><?= htmlspecialchars($order['notes']) ?></div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Chi tiết đơn hàng</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Món ăn</th>
                                    <th>Số lượng</th>
                                    <th>Đơn giá</th>
                                    <th>Thành tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($order['details'] as $detail): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($detail['food_name']) ?></td>
                                        <td><?= $detail['quantity'] ?></td>
                                        <td><?= format_currency($detail['unit_price']) ?></td>
                                        <td><?= format_currency($detail['subtotal']) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Tổng cộng:</th>
                                    <th><?= format_currency($order['total_amount']) ?></th>
                                </tr>
                                <?php if ($order['discount_percent'] > 0): ?>
                                    <tr>
                                        <th colspan="3">Giảm giá (<?= $order['discount_percent'] ?>%):</th>
                                        <th class="text-success">-<?= format_currency($order['discount_amount']) ?></th>
                                    </tr>
                                <?php endif; ?>
                                <tr class="table-success">
                                    <th colspan="3">Thành tiền:</th>
                                    <th><?= format_currency($order['final_amount']) ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Form -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Xử lý thanh toán</h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                        
                        <!-- Payment Amount -->
                        <div class="form-outline mb-4">
                            <input type="number" class="form-control form-control-lg" id="payment_amount" 
                                   name="payment_amount" value="<?= $order['final_amount'] ?>" 
                                   step="0.01" min="0" readonly required>
                            <label class="form-label" for="payment_amount">Số tiền thanh toán</label>
                        </div>

                        <!-- Payment Method -->
                        <div class="mb-4">
                            <label class="form-label required-field">Phương thức thanh toán</label>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="form-check form-check-inline w-100">
                                        <input class="form-check-input" type="radio" name="payment_method" 
                                               id="cash" value="cash" required>
                                        <label class="form-check-label w-100 p-3 border rounded text-center" for="cash">
                                            <i class="fas fa-money-bill-wave fa-2x d-block mb-2 text-success"></i>
                                            <strong>Tiền mặt</strong>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check form-check-inline w-100">
                                        <input class="form-check-input" type="radio" name="payment_method" 
                                               id="credit_card" value="credit_card" required>
                                        <label class="form-check-label w-100 p-3 border rounded text-center" for="credit_card">
                                            <i class="fas fa-credit-card fa-2x d-block mb-2 text-primary"></i>
                                            <strong>Thẻ tín dụng</strong>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check form-check-inline w-100">
                                        <input class="form-check-input" type="radio" name="payment_method" 
                                               id="debit_card" value="debit_card" required>
                                        <label class="form-check-label w-100 p-3 border rounded text-center" for="debit_card">
                                            <i class="fas fa-credit-card fa-2x d-block mb-2 text-info"></i>
                                            <strong>Thẻ ghi nợ</strong>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check form-check-inline w-100">
                                        <input class="form-check-input" type="radio" name="payment_method" 
                                               id="mobile_payment" value="mobile_payment" required>
                                        <label class="form-check-label w-100 p-3 border rounded text-center" for="mobile_payment">
                                            <i class="fas fa-mobile-alt fa-2x d-block mb-2 text-warning"></i>
                                            <strong>Thanh toán di động</strong>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Notes -->
                        <div class="form-outline mb-4">
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            <label class="form-label" for="notes">Ghi chú thanh toán (tùy chọn)</label>
                        </div>

                        <!-- Payment Summary -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="h5 mb-0">Tổng thanh toán:</span>
                                    <span class="h4 mb-0 text-success fw-bold"><?= format_currency($order['final_amount']) ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-check me-2"></i> Xác nhận thanh toán
                            </button>
                            <a href="<?= STAFF_URL ?>/payments/index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i> Hủy bỏ
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Payment method selection styling
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            // Remove active class from all labels
            paymentMethods.forEach(m => {
                m.nextElementSibling.classList.remove('border-primary', 'bg-light');
            });
            
            // Add active class to selected label
            if (this.checked) {
                this.nextElementSibling.classList.add('border-primary', 'bg-light');
            }
        });
    });

    // Form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        } else {
            // Show confirmation dialog
            const paymentAmount = document.getElementById('payment_amount').value;
            const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
            
            if (selectedMethod) {
                const methodText = selectedMethod.nextElementSibling.querySelector('strong').textContent;
                const confirmMessage = `Xác nhận thanh toán ${formatCurrency(paymentAmount)} bằng ${methodText}?`;
                
                if (!confirm(confirmMessage)) {
                    event.preventDefault();
                    event.stopPropagation();
                }
            }
        }
        
        form.classList.add('was-validated');
    });

    function formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', { 
            style: 'currency', 
            currency: 'VND',
            maximumFractionDigits: 0
        }).format(amount);
    }
});
</script>
