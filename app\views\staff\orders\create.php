<?php
require_once __DIR__ . '/../../../helpers.php';
if (!is_staff()) {
    set_error_message('<PERSON>ạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/login.php');
}
require_once __DIR__ . '/../../../models/Table.php';
require_once __DIR__ . '/../../../models/FoodItem.php';
require_once __DIR__ . '/../../../config/database.php';
$tableModel = new Table($conn);
$foodModel = new FoodItem($conn);
$tableId = isset($_GET['table_id']) ? (int)$_GET['table_id'] : 0;
$table = $tableModel->getById($tableId);
$foodItems = $foodModel->getAllWithCategories();
if (!$table) {
    set_error_message('Không tìm thấy bàn.');
    redirect(STAFF_URL . '/dashboard.php');
}
include __DIR__ . '/../../layouts/header.php';
?>
<div class="container py-4">
    <h1 class="mb-4">Tạo đơn mới cho bàn <?= htmlspecialchars($table['table_number']) ?></h1>
    <form method="POST" action="<?= STAFF_URL ?>/orders/store.php" class="needs-validation" novalidate>
        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
        <input type="hidden" name="table_id" value="<?= $table['table_id'] ?>">
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">Chọn món ăn</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Chọn</th>
                                <th>Tên món</th>
                                <th>Danh mục</th>
                                <th>Giá</th>
                                <th>Số lượng</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($foodItems as $item): ?>
                                <tr>
                                    <td><input type="checkbox" name="food_ids[]" value="<?= $item['food_id'] ?>"></td>
                                    <td><?= htmlspecialchars($item['food_name']) ?></td>
                                    <td><?= htmlspecialchars($item['category_name']) ?></td>
                                    <td><?= format_currency($item['price']) ?></td>
                                    <td><input type="number" name="quantities[<?= $item['food_id'] ?>]" min="1" max="99" value="1" class="form-control form-control-sm" style="width: 70px;"></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label for="notes" class="form-label">Ghi chú cho đơn hàng (tuỳ chọn)</label>
            <textarea name="notes" id="notes" class="form-control" rows="2"></textarea>
        </div>
        <button type="submit" class="btn btn-success">
            <i class="fas fa-save"></i> Lưu đơn hàng
        </button>
        <a href="<?= STAFF_URL ?>/dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </form>
</div>
<?php include __DIR__ . '/../../layouts/footer.php'; ?>