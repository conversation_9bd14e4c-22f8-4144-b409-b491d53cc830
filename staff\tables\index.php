<?php
/**
 * Staff Tables Management
 */
require_once '../../app/config/config.php';
require_once '../../app/config/database.php';
require_once '../../app/helpers.php';
require_once '../../app/models/Table.php';
require_once '../../app/models/Order.php';

// Check if user is staff
if (!is_staff()) {
    set_error_message('Bạn không có quyền truy cập trang này');
    redirect(BASE_URL);
}

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Initialize models
$tableModel = new Table($conn);
$orderModel = new Order($conn);

// Get current user
$currentUser = get_logged_in_user();

// Handle table status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    // Verify CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        set_error_message('Token bảo mật không hợp lệ');
    } else {
        $action = $_POST['action'];
        $tableId = (int)($_POST['table_id'] ?? 0);

        switch ($action) {
            case 'set_maintenance':
                if ($tableModel->update($tableId, ['status' => 'maintenance'])) {
                    set_success_message('Đã chuyển bàn sang trạng thái bảo trì');
                } else {
                    set_error_message('Có lỗi xảy ra khi cập nhật trạng thái bàn');
                }
                break;

            case 'set_available':
                if ($tableModel->update($tableId, ['status' => 'available'])) {
                    set_success_message('Đã chuyển bàn sang trạng thái trống');
                } else {
                    set_error_message('Có lỗi xảy ra khi cập nhật trạng thái bàn');
                }
                break;

            case 'set_reserved':
                if ($tableModel->update($tableId, ['status' => 'reserved'])) {
                    set_success_message('Đã đặt trước bàn');
                } else {
                    set_error_message('Có lỗi xảy ra khi đặt trước bàn');
                }
                break;
        }

        redirect(STAFF_URL . '/tables/index.php');
    }
}

// Get all tables with their current orders
$tables = $tableModel->getAll('table_number ASC');

// Get active orders for occupied tables
$activeOrders = [];
foreach ($tables as $table) {
    if ($table['status'] === 'occupied') {
        $orders = $orderModel->findBy(
            'table_id = :table_id AND status IN (:status1, :status2)',
            [
                ':table_id' => $table['table_id'],
                ':status1' => 'pending',
                ':status2' => 'processing'
            ],
            'order_date DESC',
            1
        );
        if (!empty($orders)) {
            $activeOrders[$table['table_id']] = $orders[0];
        }
    }
}

// Get table status counts
$tableStatus = $tableModel->getStatusCounts();

// Set page title
$pageTitle = 'Quản lý bàn';

// Include content in layout
$content = VIEWS_DIR . '/staff/tables/index.php';
include_once VIEWS_DIR . '/layouts/main.php';
