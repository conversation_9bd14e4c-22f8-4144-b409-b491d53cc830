<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <h1>Thêm món ăn mới</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= ADMIN_URL ?>/dashboard.php">Bảng điều khiển</a></li>
                    <li class="breadcrumb-item"><a href="<?= ADMIN_URL ?>/food/index.php">Quản lý món ăn</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Thêm món ăn mới</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="" method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                
                <div class="row">
                    <div class="col-md-8">
                        <!-- Food Name -->
                        <div class="form-outline mb-4 <?= isset($errors['food_name']) ? 'is-invalid' : '' ?>">
                            <input type="text" id="food_name" name="food_name" class="form-control" value="<?= htmlspecialchars($food['food_name']) ?>" required>
                            <label class="form-label" for="food_name">Tên món ăn <span class="text-danger">*</span></label>
                            <?php if (isset($errors['food_name'])): ?>
                                <div class="invalid-feedback"><?= $errors['food_name'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Category -->
                        <div class="form-outline mb-4 <?= isset($errors['category_id']) ? 'is-invalid' : '' ?>">
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">-- Chọn danh mục --</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['category_id'] ?>" <?= $food['category_id'] == $category['category_id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category['category_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <label class="form-label" for="category_id">Danh mục <span class="text-danger">*</span></label>
                            <?php if (isset($errors['category_id'])): ?>
                                <div class="invalid-feedback"><?= $errors['category_id'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Price -->
                        <div class="form-outline mb-4 <?= isset($errors['price']) ? 'is-invalid' : '' ?>">
                            <input type="number" id="price" name="price" class="form-control" min="0" step="1000" value="<?= htmlspecialchars($food['price']) ?>" required>
                            <label class="form-label" for="price">Giá (VNĐ) <span class="text-danger">*</span></label>
                            <?php if (isset($errors['price'])): ?>
                                <div class="invalid-feedback"><?= $errors['price'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Description -->
                        <div class="form-outline mb-4">
                            <textarea id="description" name="description" class="form-control" rows="5"><?= htmlspecialchars($food['description']) ?></textarea>
                            <label class="form-label" for="description">Mô tả</label>
                        </div>
                        
                        <!-- Status -->
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="status" name="status" value="1" <?= $food['status'] == 1 ? 'checked' : '' ?>>
                            <label class="form-check-label" for="status">Còn món (có sẵn để đặt)</label>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <!-- Image Upload -->
                        <div class="card mb-4">
                            <div class="card-header">Hình ảnh món ăn</div>
                            <div class="card-body text-center">
                                <?php $placeholderData = get_food_image(null, 'large'); ?>
                                <img id="imagePreview" src="<?= $placeholderData['src'] ?>"
                                     alt="<?= $placeholderData['alt'] ?>"
                                     class="<?= $placeholderData['class'] ?> mb-3"
                                     data-placeholder="<?= $placeholderData['src'] ?>">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                </div>
                                <?php if (isset($errors['image'])): ?>
                                    <div class="text-danger mt-2"><?= $errors['image'] ?></div>
                                <?php endif; ?>
                                <small class="form-text text-muted mt-2">
                                    Cho phép: JPG, PNG, GIF, WEBP. Kích thước tối đa: 2MB.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 d-flex justify-content-between">
                    <a href="<?= ADMIN_URL ?>/food/index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Quay lại
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> Lưu món ăn
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>