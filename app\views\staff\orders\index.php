<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="mb-0">Quản lý đơn hàng</h1>
            <p class="text-muted"><PERSON>h sách đơn hàng của bạn</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= STAFF_URL ?>/orders/create.php" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Tạo đơn hàng mới
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">Trạng thái</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Tất cả trạng thái</option>
                        <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>Đang chờ</option>
                        <option value="processing" <?= $status === 'processing' ? 'selected' : '' ?>>Đang xử lý</option>
                        <option value="completed" <?= $status === 'completed' ? 'selected' : '' ?>>Hoàn thành</option>
                        <option value="cancelled" <?= $status === 'cancelled' ? 'selected' : '' ?>>Đã hủy</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date" class="form-label">Ngày</label>
                    <input type="date" class="form-control" id="date" name="date" value="<?= htmlspecialchars($date) ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i> Lọc
                    </button>
                    <a href="<?= STAFF_URL ?>/orders/index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> Xóa bộ lọc
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders List -->
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Danh sách đơn hàng (<?= $totalOrders ?> đơn)</h5>
        </div>
        <div class="card-body">
            <?php if (empty($ordersWithDetails)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Không có đơn hàng nào</h5>
                    <p class="text-muted">Bạn chưa tạo đơn hàng nào hoặc không có đơn hàng phù hợp với bộ lọc.</p>
                    <a href="<?= STAFF_URL ?>/orders/create.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Tạo đơn hàng mới
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Bàn</th>
                                <th>Ngày giờ</th>
                                <th>Tổng tiền</th>
                                <th>Giảm giá</th>
                                <th>Thành tiền</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ordersWithDetails as $order): ?>
                                <tr>
                                    <td><?= $order['order_id'] ?></td>
                                    <td>
                                        <span class="fw-bold"><?= $order['table_number'] ?></span>
                                    </td>
                                    <td><?= date('d/m/Y H:i', strtotime($order['order_date'])) ?></td>
                                    <td><?= format_currency($order['total_amount']) ?></td>
                                    <td>
                                        <?php if ($order['discount_percent'] > 0): ?>
                                            <span class="text-success">
                                                -<?= $order['discount_percent'] ?>%
                                                (<?= format_currency($order['discount_amount']) ?>)
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="fw-bold"><?= format_currency($order['final_amount']) ?></td>
                                    <td>
                                        <?php
                                        $statusClass = '';
                                        $statusText = '';
                                        
                                        switch ($order['status']) {
                                            case 'pending':
                                                $statusClass = 'bg-warning';
                                                $statusText = 'Đang chờ';
                                                break;
                                            case 'processing':
                                                $statusClass = 'bg-info';
                                                $statusText = 'Đang xử lý';
                                                break;
                                            case 'completed':
                                                $statusClass = 'bg-success';
                                                $statusText = 'Hoàn thành';
                                                break;
                                            case 'cancelled':
                                                $statusClass = 'bg-danger';
                                                $statusText = 'Đã hủy';
                                                break;
                                            default:
                                                $statusClass = 'bg-secondary';
                                                $statusText = 'Không xác định';
                                                break;
                                        }
                                        ?>
                                        <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= STAFF_URL ?>/orders/view.php?id=<?= $order['order_id'] ?>" 
                                               class="btn btn-outline-primary" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (in_array($order['status'], ['pending', 'processing'])): ?>
                                                <a href="<?= STAFF_URL ?>/orders/edit.php?id=<?= $order['order_id'] ?>" 
                                                   class="btn btn-outline-warning" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if ($order['status'] === 'completed'): ?>
                                                <a href="<?= STAFF_URL ?>/payments/create.php?order_id=<?= $order['order_id'] ?>" 
                                                   class="btn btn-outline-success" title="Thanh toán">
                                                    <i class="fas fa-credit-card"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Phân trang đơn hàng" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page - 1 ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page + 1 ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>
