<?php
require_once APP_DIR . '/models/BaseModel.php';

/**
 * Order Model
 */
class Order extends BaseModel {
    /**
     * Constructor
     *
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        parent::__construct($db, 'orders');
        $this->primaryKey = 'order_id';
    }

    /**
     * Get all orders with table and staff info
     *
     * @param string $orderBy Order by clause
     * @param int $limit Limit results
     * @param int $offset Offset for pagination
     * @return array Orders with related info
     */
    public function getAllWithDetails($orderBy = 'o.order_date DESC', $limit = null, $offset = null) {
        $sql = "SELECT o.*, t.table_number, u.full_name as staff_name 
                FROM orders o 
                JOIN tables t ON o.table_id = t.table_id 
                JOIN users u ON o.user_id = u.user_id";
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
            
            if ($offset) {
                $sql .= " OFFSET {$offset}";
            }
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }

    /**
     * Get active orders
     *
     * @return array Active orders
     */
    public function getActiveOrders() {
        return $this->findBy(
            'status IN (:status1, :status2)',
            [
                ':status1' => 'pending',
                ':status2' => 'processing'
            ],
            'order_date DESC'
        );
    }

    /**
     * Get active orders by user (pending or processing)
     *
     * @param int $userId User ID
     * @return array Active orders for the user
     */
    public function getActiveOrdersByUser($userId) {
        return $this->findBy(
            'user_id = :user_id AND status IN (:status1, :status2)',
            [
                ':user_id' => $userId,
                ':status1' => 'pending',
                ':status2' => 'processing'
            ],
            'order_date DESC'
        );
    }

    /**
     * Get order details
     *
     * @param int $orderId Order ID
     * @return array Order details
     */
    public function getOrderDetails($orderId) {
        $sql = "SELECT od.*, f.food_name, f.price 
                FROM order_details od 
                JOIN food_items f ON od.food_id = f.food_id 
                WHERE od.order_id = :order_id";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':order_id', $orderId);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }

    /**
     * Get order with details
     *
     * @param int $orderId Order ID
     * @return array|null Order with details or null if not found
     */
    public function getOrderWithDetails($orderId) {
        $order = $this->getById($orderId);
        
        if (!$order) {
            return null;
        }
        
        $order['details'] = $this->getOrderDetails($orderId);
        
        // Get table info
        $sql = "SELECT table_number FROM tables WHERE table_id = :table_id";
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':table_id', $order['table_id']);
        $stmt->execute();
        $table = $stmt->fetch();
        
        $order['table_number'] = $table['table_number'];
        
        // Get staff info
        $sql = "SELECT full_name FROM users WHERE user_id = :user_id";
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':user_id', $order['user_id']);
        $stmt->execute();
        $user = $stmt->fetch();
        
        $order['staff_name'] = $user['full_name'];
        
        return $order;
    }

    /**
     * Create a new order
     *
     * @param array $data Order data
     * @return int|bool Order ID or false on failure
     */
    public function create($data) {
        $this->beginTransaction();

        try {
            $orderId = parent::create($data);

            if ($orderId) {
                // Update table status to occupied
                $sql = "UPDATE tables SET status = 'occupied' WHERE table_id = :table_id";
                $stmt = $this->conn->prepare($sql);
                $stmt->bindValue(':table_id', $data['table_id']);
                $stmt->execute();

                $this->commit();
                return $orderId;
            }

            $this->rollback();
            return false;
        } catch (Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * Create a new order with items in a single transaction
     *
     * @param array $orderData Order data
     * @param array $orderItems Array of order items
     * @return int|bool Order ID or false on failure
     */
    public function createOrderWithItems($orderData, $orderItems) {
        $this->beginTransaction();

        try {
            // Create order
            $orderId = parent::create($orderData);

            if (!$orderId) {
                $this->rollback();
                return false;
            }

            // Update table status to occupied
            $sql = "UPDATE tables SET status = 'occupied' WHERE table_id = :table_id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindValue(':table_id', $orderData['table_id']);
            $stmt->execute();

            // Add order items
            foreach ($orderItems as $item) {
                $result = $this->addOrderItemInternal(
                    $orderId,
                    $item['food_id'],
                    $item['quantity'],
                    $item['unit_price'],
                    $item['notes'] ?? ''
                );

                if (!$result) {
                    $this->rollback();
                    return false;
                }
            }

            // Recalculate order totals to ensure they are correct
            $this->recalculateOrderTotalsInternal($orderId);

            $this->commit();
            return $orderId;
        } catch (Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * Add item to order
     *
     * @param int $orderId Order ID
     * @param int $foodId Food ID
     * @param int $quantity Quantity
     * @param float $unitPrice Unit price
     * @param string $notes Notes
     * @return int|bool Order detail ID or false on failure
     */
    public function addOrderItem($orderId, $foodId, $quantity, $unitPrice, $notes = '') {
        $subtotal = $quantity * $unitPrice;

        $sql = "INSERT INTO order_details (order_id, food_id, quantity, unit_price, subtotal, notes)
                VALUES (:order_id, :food_id, :quantity, :unit_price, :subtotal, :notes)";

        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':order_id', $orderId);
        $stmt->bindValue(':food_id', $foodId);
        $stmt->bindValue(':quantity', $quantity);
        $stmt->bindValue(':unit_price', $unitPrice);
        $stmt->bindValue(':subtotal', $subtotal);
        $stmt->bindValue(':notes', $notes);

        if ($stmt->execute()) {
            return $this->conn->lastInsertId();
        }

        return false;
    }

    /**
     * Internal method to add item to order (used within transactions)
     *
     * @param int $orderId Order ID
     * @param int $foodId Food ID
     * @param int $quantity Quantity
     * @param float $unitPrice Unit price
     * @param string $notes Notes
     * @return int|bool Order detail ID or false on failure
     */
    private function addOrderItemInternal($orderId, $foodId, $quantity, $unitPrice, $notes = '') {
        $subtotal = $quantity * $unitPrice;

        $sql = "INSERT INTO order_details (order_id, food_id, quantity, unit_price, subtotal, notes)
                VALUES (:order_id, :food_id, :quantity, :unit_price, :subtotal, :notes)";

        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':order_id', $orderId);
        $stmt->bindValue(':food_id', $foodId);
        $stmt->bindValue(':quantity', $quantity);
        $stmt->bindValue(':unit_price', $unitPrice);
        $stmt->bindValue(':subtotal', $subtotal);
        $stmt->bindValue(':notes', $notes);

        if ($stmt->execute()) {
            return $this->conn->lastInsertId();
        }

        return false;
    }

    /**
     * Update order item
     *
     * @param int $orderDetailId Order detail ID
     * @param int $quantity New quantity
     * @param string $notes New notes
     * @return bool True on success, false on failure
     */
    public function updateOrderItem($orderDetailId, $quantity, $notes = '') {
        $sql = "UPDATE order_details 
                SET quantity = :quantity, subtotal = quantity * unit_price, notes = :notes 
                WHERE order_detail_id = :order_detail_id";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':quantity', $quantity);
        $stmt->bindValue(':notes', $notes);
        $stmt->bindValue(':order_detail_id', $orderDetailId);
        
        return $stmt->execute();
    }

    /**
     * Remove item from order
     *
     * @param int $orderDetailId Order detail ID
     * @return bool True on success, false on failure
     */
    public function removeOrderItem($orderDetailId) {
        $sql = "DELETE FROM order_details WHERE order_detail_id = :order_detail_id";
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':order_detail_id', $orderDetailId);
        
        return $stmt->execute();
    }

    /**
     * Apply discount to order
     *
     * @param int $orderId Order ID
     * @param float $discountPercent Discount percentage
     * @return bool True on success, false on failure
     */
    public function applyDiscount($orderId, $discountPercent) {
        $sql = "UPDATE orders 
                SET discount_percent = :discount_percent, 
                    discount_amount = total_amount * (:discount_percent / 100),
                    final_amount = total_amount - (total_amount * (:discount_percent / 100))
                WHERE order_id = :order_id";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':discount_percent', $discountPercent);
        $stmt->bindValue(':order_id', $orderId);
        
        return $stmt->execute();
    }

    /**
     * Complete order (change status to completed)
     *
     * @param int $orderId Order ID
     * @return bool True on success, false on failure
     */
    public function completeOrder($orderId) {
        $this->beginTransaction();
        
        try {
            // Update order status
            $sql = "UPDATE orders SET status = 'completed' WHERE order_id = :order_id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindValue(':order_id', $orderId);
            $result = $stmt->execute();
            
            if ($result) {
                // Free up table
                $sql = "UPDATE tables t 
                        JOIN orders o ON t.table_id = o.table_id 
                        SET t.status = 'available' 
                        WHERE o.order_id = :order_id";
                
                $stmt = $this->conn->prepare($sql);
                $stmt->bindValue(':order_id', $orderId);
                $stmt->execute();
                
                $this->commit();
                return true;
            }
            
            $this->rollback();
            return false;
        } catch (Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * Cancel order
     *
     * @param int $orderId Order ID
     * @return bool True on success, false on failure
     */
    public function cancelOrder($orderId) {
        $this->beginTransaction();
        
        try {
            // Update order status
            $sql = "UPDATE orders SET status = 'cancelled' WHERE order_id = :order_id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindValue(':order_id', $orderId);
            $result = $stmt->execute();
            
            if ($result) {
                // Free up table
                $sql = "UPDATE tables t 
                        JOIN orders o ON t.table_id = o.table_id 
                        SET t.status = 'available' 
                        WHERE o.order_id = :order_id";
                
                $stmt = $this->conn->prepare($sql);
                $stmt->bindValue(':order_id', $orderId);
                $stmt->execute();
                
                $this->commit();
                return true;
            }
            
            $this->rollback();
            return false;
        } catch (Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * Transfer table
     *
     * @param int $orderId Order ID
     * @param int $fromTableId From table ID
     * @param int $toTableId To table ID
     * @param int $userId User ID
     * @param string $reason Reason for transfer
     * @return bool True on success, false on failure
     */
    public function transferTable($orderId, $fromTableId, $toTableId, $userId, $reason = '') {
        $this->beginTransaction();
        
        try {
            // Update order's table
            $sql = "UPDATE orders SET table_id = :to_table_id WHERE order_id = :order_id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindValue(':to_table_id', $toTableId);
            $stmt->bindValue(':order_id', $orderId);
            $stmt->execute();
            
            // Update table statuses
            $sql = "UPDATE tables SET status = 'available' WHERE table_id = :from_table_id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindValue(':from_table_id', $fromTableId);
            $stmt->execute();
            
            $sql = "UPDATE tables SET status = 'occupied' WHERE table_id = :to_table_id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindValue(':to_table_id', $toTableId);
            $stmt->execute();
            
            // Log transfer
            $sql = "INSERT INTO table_transfers (order_id, from_table_id, to_table_id, transferred_by, reason) 
                    VALUES (:order_id, :from_table_id, :to_table_id, :user_id, :reason)";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindValue(':order_id', $orderId);
            $stmt->bindValue(':from_table_id', $fromTableId);
            $stmt->bindValue(':to_table_id', $toTableId);
            $stmt->bindValue(':user_id', $userId);
            $stmt->bindValue(':reason', $reason);
            $stmt->execute();
            
            $this->commit();
            return true;
        } catch (Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * Get daily revenue
     *
     * @param string $date Date in YYYY-MM-DD format
     * @return array Revenue data
     */
    public function getDailyRevenue($date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        $sql = "SELECT SUM(p.payment_amount) as total_revenue, COUNT(p.payment_id) as order_count 
                FROM payments p 
                WHERE DATE(p.payment_date) = :date AND p.payment_status = 'completed'";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':date', $date);
        $stmt->execute();
        
        return $stmt->fetch();
    }

    /**
     * Get weekly revenue
     *
     * @param string $startDate Start date in YYYY-MM-DD format
     * @param string $endDate End date in YYYY-MM-DD format
     * @return array Revenue data
     */
    public function getWeeklyRevenue($startDate = null, $endDate = null) {
        if (!$startDate) {
            $startDate = date('Y-m-d', strtotime('monday this week'));
        }
        
        if (!$endDate) {
            $endDate = date('Y-m-d', strtotime('sunday this week'));
        }
        
        $sql = "SELECT DATE(p.payment_date) as date, 
                       SUM(p.payment_amount) as daily_revenue, 
                       COUNT(p.payment_id) as order_count 
                FROM payments p 
                WHERE DATE(p.payment_date) BETWEEN :start_date AND :end_date 
                  AND p.payment_status = 'completed' 
                GROUP BY DATE(p.payment_date) 
                ORDER BY date";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':start_date', $startDate);
        $stmt->bindValue(':end_date', $endDate);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }

    /**
     * Get monthly revenue
     *
     * @param int $month Month (1-12)
     * @param int $year Year
     * @return array Revenue data
     */
    public function getMonthlyRevenue($month = null, $year = null) {
        if (!$month) {
            $month = date('m');
        }
        
        if (!$year) {
            $year = date('Y');
        }
        
        $sql = "SELECT DATE(p.payment_date) as date, 
                       SUM(p.payment_amount) as daily_revenue 
                FROM payments p 
                WHERE MONTH(p.payment_date) = :month 
                  AND YEAR(p.payment_date) = :year 
                  AND p.payment_status = 'completed' 
                GROUP BY DATE(p.payment_date) 
                ORDER BY date";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':month', $month);
        $stmt->bindValue(':year', $year);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }

    /**
     * Get popular food items for reporting
     *
     * @param string $startDate Start date in YYYY-MM-DD format
     * @param string $endDate End date in YYYY-MM-DD format
     * @param int $limit Limit results
     * @return array Popular food items
     */
    public function getPopularFoodItems($startDate = null, $endDate = null, $limit = 10) {
        $whereClause = "o.status = 'completed'";
        $params = [];

        if ($startDate && $endDate) {
            $whereClause .= " AND DATE(o.order_date) BETWEEN :start_date AND :end_date";
            $params[':start_date'] = $startDate;
            $params[':end_date'] = $endDate;
        }

        $sql = "SELECT f.food_id, f.food_name, c.category_name,
                       SUM(od.quantity) as total_quantity,
                       SUM(od.subtotal) as total_revenue
                FROM order_details od
                JOIN food_items f ON od.food_id = f.food_id
                JOIN categories c ON f.category_id = c.category_id
                JOIN orders o ON od.order_id = o.order_id
                WHERE {$whereClause}
                GROUP BY f.food_id, f.food_name, c.category_name
                ORDER BY total_quantity DESC
                LIMIT :limit";

        $stmt = $this->conn->prepare($sql);

        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * Recalculate order totals manually
     *
     * @param int $orderId Order ID
     * @return bool True on success, false on failure
     */
    public function recalculateOrderTotals($orderId) {
        try {
            return $this->recalculateOrderTotalsInternal($orderId);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Internal method to recalculate order totals (used within transactions)
     *
     * @param int $orderId Order ID
     * @return bool True on success, false on failure
     */
    private function recalculateOrderTotalsInternal($orderId) {
        // Get order details total
        $sql = "SELECT SUM(subtotal) as total FROM order_details WHERE order_id = :order_id";
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':order_id', $orderId);
        $stmt->execute();
        $result = $stmt->fetch();

        $totalAmount = $result['total'] ?? 0;

        // Get current discount percentage
        $order = $this->getById($orderId);
        $discountPercent = $order['discount_percent'] ?? 0;

        // Calculate discount amount and final amount
        $discountAmount = $totalAmount * ($discountPercent / 100);
        $finalAmount = $totalAmount - $discountAmount;

        // Update order totals
        $sql = "UPDATE orders SET
                total_amount = :total_amount,
                discount_amount = :discount_amount,
                final_amount = :final_amount
                WHERE order_id = :order_id";

        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':total_amount', $totalAmount);
        $stmt->bindValue(':discount_amount', $discountAmount);
        $stmt->bindValue(':final_amount', $finalAmount);
        $stmt->bindValue(':order_id', $orderId);

        return $stmt->execute();
    }
}