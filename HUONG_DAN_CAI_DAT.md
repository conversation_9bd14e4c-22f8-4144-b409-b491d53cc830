# Hướng Dẫn Cài Đặt và Cấu <PERSON>nh - <PERSON><PERSON> Thống Quản Lý Quán Cafe

## <PERSON>ụ<PERSON>
1. [Y<PERSON><PERSON>](#yêu-cầu-hệ-thống)
2. [Cài Đặt Môi Trường](#cài-đặt-môi-trường)
3. [Cài Đặt Ứng Dụng](#cài-đặt-ứng-dụng)
4. [<PERSON>ấu <PERSON>nh <PERSON> Sở Dữ Liệu](#cấu-hình-cơ-sở-dữ-liệu)
5. [<PERSON><PERSON><PERSON>nh Ứng Dụng](#cấu-hình-ứng-dụng)
6. [Khởi Chạy Ứng Dụng](#khởi-chạy-ứng-dụng)
7. [Tà<PERSON> Mặc Định](#tài-khoản-mặc-định)
8. [X<PERSON> Lý Sự Cố](#xử-lý-sự-cố)

## Y<PERSON><PERSON>ố<PERSON>

### <PERSON><PERSON><PERSON>ềm <PERSON>
- **PHP**: <PERSON><PERSON><PERSON> b<PERSON> 7.4 hoặc cao hơn
- **MySQL**: <PERSON><PERSON><PERSON> bản 5.7 hoặc cao hơn (hoặc MariaDB 10.2+)
- **Web Server**: Apache hoặc Nginx
- **Composer**: Để quản lý dependencies (tùy chọn)

### Extensions PHP Cần Thiết
- `pdo`
- `pdo_mysql`
- `mbstring`
- `json`
- `session`
- `gd` (cho xử lý hình ảnh)
- `fileinfo` (cho upload file)

### Yêu Cầu Phần Cứng Tối Thiểu
- **RAM**: 512MB
- **Ổ cứng**: 100MB dung lượng trống
- **CPU**: 1GHz

## Cài Đặt Môi Trường

### Tùy Chọn 1: Sử Dụng XAMPP (Khuyến Nghị cho Windows)

1. **Tải XAMPP**:
   - Truy cập: https://www.apachefriends.org/
   - Tải phiên bản phù hợp với hệ điều hành

2. **Cài Đặt XAMPP**:
   - Chạy file cài đặt với quyền Administrator
   - Chọn các thành phần: Apache, MySQL, PHP, phpMyAdmin
   - Cài đặt vào thư mục mặc định: `C:\xampp`

3. **Khởi Động Dịch Vụ**:
   - Mở XAMPP Control Panel
   - Start Apache và MySQL

### Tùy Chọn 2: Sử Dụng Laragon (Khuyến Nghị cho Windows)

1. **Tải Laragon**:
   - Truy cập: https://laragon.org/
   - Tải phiên bản Full

2. **Cài Đặt Laragon**:
   - Chạy file cài đặt
   - Cài đặt vào thư mục mặc định

3. **Khởi Động Dịch Vụ**:
   - Mở Laragon
   - Click "Start All"

### Tùy Chọn 3: Cài Đặt Thủ Công (Linux/Ubuntu)

```bash
# Cập nhật hệ thống
sudo apt update && sudo apt upgrade -y

# Cài đặt Apache
sudo apt install apache2 -y

# Cài đặt MySQL
sudo apt install mysql-server -y

# Cài đặt PHP và extensions
sudo apt install php php-mysql php-mbstring php-json php-gd php-fileinfo -y

# Khởi động dịch vụ
sudo systemctl start apache2
sudo systemctl start mysql
sudo systemctl enable apache2
sudo systemctl enable mysql
```

## Cài Đặt Ứng Dụng

### Bước 1: Tải Mã Nguồn

**Từ Git Repository:**
```bash
git clone [URL_REPOSITORY] do-an
cd do-an
```

**Hoặc giải nén từ file ZIP:**
- Giải nén file vào thư mục web root
- Đổi tên thư mục thành `do-an`

### Bước 2: Đặt Vào Thư Mục Web Root

**XAMPP:**
```
C:\xampp\htdocs\do-an\
```

**Laragon:**
```
C:\laragon\www\do-an\
```

**Linux/Apache:**
```
/var/www/html/do-an/
```

### Bước 3: Phân Quyền (Linux)

```bash
# Phân quyền cho thư mục uploads
sudo chmod 755 public/uploads/
sudo chown -R www-data:www-data public/uploads/

# Phân quyền cho toàn bộ ứng dụng
sudo chown -R www-data:www-data /var/www/html/do-an/
sudo chmod -R 755 /var/www/html/do-an/
```

## Cấu Hình Cơ Sở Dữ Liệu

### Bước 1: Tạo Cơ Sở Dữ Liệu

**Sử dụng phpMyAdmin:**
1. Truy cập: http://localhost/phpmyadmin
2. Đăng nhập (user: root, password: để trống hoặc theo cấu hình)
3. Tạo database mới tên: `restaurant_management`
4. Chọn Collation: `utf8mb4_unicode_ci`

**Sử dụng MySQL Command Line:**
```sql
mysql -u root -p
CREATE DATABASE restaurant_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;
```

### Bước 2: Import Dữ Liệu

**Sử dụng phpMyAdmin:**
1. Chọn database `restaurant_management`
2. Click tab "Import"
3. Chọn file `database.sql`
4. Click "Go"

**Sử dụng MySQL Command Line:**
```bash
mysql -u root -p restaurant_management < database.sql
```

### Bước 3: Tạo User Database (Tùy Chọn)

```sql
mysql -u root -p
CREATE USER 'restaurant_user'@'localhost' IDENTIFIED BY 'password123';
GRANT ALL PRIVILEGES ON restaurant_management.* TO 'restaurant_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

## Cấu Hình Ứng Dụng

### Bước 1: Cấu Hình Database

Chỉnh sửa file `app/config/database.php`:

```php
<?php
class Database {
    private $host = 'localhost';
    private $db_name = 'restaurant_management';
    private $username = 'root';           // Hoặc 'restaurant_user'
    private $password = '';               // Hoặc 'password123'
    // ... phần còn lại giữ nguyên
}
```

### Bước 2: Cấu Hình Ứng Dụng

Chỉnh sửa file `app/config/config.php`:

```php
// Thay đổi BASE_URL theo môi trường của bạn
define('BASE_URL', 'http://localhost/do-an');

// Hoặc nếu sử dụng virtual host
define('BASE_URL', 'http://do-an.test');

// Tên ứng dụng
define('APP_NAME', 'Quản Lý Quán Cafe');
```

### Bước 3: Cấu Hình Virtual Host (Tùy Chọn)

**Apache (Windows/XAMPP):**

Thêm vào file `C:\xampp\apache\conf\extra\httpd-vhosts.conf`:
```apache
<VirtualHost *:80>
    DocumentRoot "C:/xampp/htdocs/do-an"
    ServerName do-an.test
    <Directory "C:/xampp/htdocs/do-an">
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

Thêm vào file `C:\Windows\System32\drivers\etc\hosts`:
```
127.0.0.1 do-an.test
```

**Laragon:**
- Laragon tự động tạo virtual host
- Truy cập: http://do-an.test

## Khởi Chạy Ứng Dụng

### Bước 1: Kiểm Tra Dịch Vụ

Đảm bảo Apache và MySQL đang chạy:
- XAMPP: Kiểm tra Control Panel
- Laragon: Kiểm tra status
- Linux: `sudo systemctl status apache2 mysql`

### Bước 2: Truy Cập Ứng Dụng

Mở trình duyệt và truy cập:
- http://localhost/do-an
- Hoặc http://do-an.test (nếu đã cấu hình virtual host)

### Bước 3: Kiểm Tra Kết Nối

Nếu thành công, bạn sẽ được chuyển hướng đến trang đăng nhập.

## Tài Khoản Mặc Định

Sau khi import database, bạn cần tạo tài khoản admin đầu tiên:

```sql
-- Tạo roles
INSERT INTO roles (role_name) VALUES ('admin'), ('staff'), ('customer');

-- Tạo tài khoản admin (password: admin123)
INSERT INTO users (username, password, email, full_name, role_id, status) 
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
        '<EMAIL>', 'Administrator', 1, 1);

-- Tạo tài khoản staff (password: staff123)
INSERT INTO users (username, password, email, full_name, role_id, status) 
VALUES ('staff', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
        '<EMAIL>', 'Nhân Viên', 2, 1);
```

**Thông tin đăng nhập:**
- **Admin**: username: `admin`, password: `admin123`
- **Staff**: username: `staff`, password: `staff123`

## Xử Lý Sự Cố

### Lỗi Kết Nối Database

**Triệu chứng:** "Connection Error" hoặc không thể kết nối database

**Giải pháp:**
1. Kiểm tra MySQL đang chạy
2. Kiểm tra thông tin kết nối trong `database.php`
3. Kiểm tra database đã được tạo chưa
4. Kiểm tra quyền user database

### Lỗi 404 Not Found

**Triệu chứng:** Trang không tìm thấy

**Giải pháp:**
1. Kiểm tra đường dẫn file
2. Kiểm tra Apache đang chạy
3. Kiểm tra cấu hình virtual host
4. Kiểm tra file .htaccess (nếu có)

### Lỗi Permission Denied

**Triệu chứng:** Không thể upload file hoặc ghi dữ liệu

**Giải pháp:**
```bash
# Linux
sudo chmod 755 public/uploads/
sudo chown -R www-data:www-data public/uploads/

# Windows: Click chuột phải > Properties > Security > Edit
```

### Lỗi PHP Extensions

**Triệu chứng:** "Call to undefined function" hoặc extension không tìm thấy

**Giải pháp:**
1. Kiểm tra PHP extensions đã cài đặt: `php -m`
2. Cài đặt extensions thiếu
3. Restart web server

### Lỗi Session

**Triệu chứng:** Không thể đăng nhập hoặc session bị mất

**Giải pháp:**
1. Kiểm tra quyền ghi thư mục session
2. Kiểm tra cấu hình session trong PHP
3. Xóa cache trình duyệt

## Liên Hệ Hỗ Trợ

Nếu gặp vấn đề trong quá trình cài đặt, vui lòng:
1. Kiểm tra log lỗi Apache/PHP
2. Kiểm tra log MySQL
3. Tham khảo tài liệu PHP/MySQL
4. Liên hệ nhóm phát triển

---

**Chúc bạn cài đặt thành công!** 🎉
