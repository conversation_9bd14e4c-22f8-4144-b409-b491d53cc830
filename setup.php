<?php
/**
 * Script Cài Đặt Tự Động - Hệ Thống Quản Lý Quán Cafe
 * 
 * Script này sẽ giúp bạn cài đặt và cấu hình ứng dụng một cách tự động
 */

// Kiểm tra PHP version
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    die("❌ PHP 7.4.0 hoặc cao hơn là bắt buộc. Phiên bản hiện tại: " . PHP_VERSION . "\n");
}

echo "🚀 Bắt đầu cài đặt Hệ Thống Quản Lý Quán Cafe...\n\n";

// Kiểm tra extensions PHP cần thiết
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'session', 'gd', 'fileinfo'];
$missing_extensions = [];

foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $missing_extensions[] = $ext;
    }
}

if (!empty($missing_extensions)) {
    echo "❌ Thiếu PHP extensions: " . implode(', ', $missing_extensions) . "\n";
    echo "Vui lòng cài đặt các extensions này trước khi tiếp tục.\n";
    exit(1);
}

echo "✅ Kiểm tra PHP extensions: OK\n";

// Tạo thư mục cần thiết
$directories = [
    'public/uploads',
    'public/uploads/food',
    'public/uploads/users',
    'logs',
    'cache'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ Tạo thư mục: {$dir}\n";
        } else {
            echo "❌ Không thể tạo thư mục: {$dir}\n";
        }
    } else {
        echo "✅ Thư mục đã tồn tại: {$dir}\n";
    }
}

// Kiểm tra quyền ghi
$writable_dirs = ['public/uploads', 'logs', 'cache'];
foreach ($writable_dirs as $dir) {
    if (is_writable($dir)) {
        echo "✅ Quyền ghi OK: {$dir}\n";
    } else {
        echo "❌ Không có quyền ghi: {$dir}\n";
        echo "   Chạy: chmod 755 {$dir}\n";
    }
}

echo "\n📋 Cấu hình cơ sở dữ liệu...\n";

// Thu thập thông tin database
echo "Nhập thông tin kết nối cơ sở dữ liệu:\n";
$db_host = readline("Host (localhost): ") ?: 'localhost';
$db_name = readline("Tên database (restaurant_management): ") ?: 'restaurant_management';
$db_user = readline("Username (root): ") ?: 'root';
$db_pass = readline("Password: ");

// Kiểm tra kết nối database
try {
    $pdo = new PDO("mysql:host={$db_host}", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Kết nối database thành công\n";
    
    // Tạo database nếu chưa tồn tại
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database '{$db_name}' đã sẵn sàng\n";
    
} catch (PDOException $e) {
    echo "❌ Lỗi kết nối database: " . $e->getMessage() . "\n";
    exit(1);
}

// Cập nhật file cấu hình database
$db_config = "<?php
/**
 * Database Configuration
 */
class Database {
    private \$host = '{$db_host}';
    private \$db_name = '{$db_name}';
    private \$username = '{$db_user}';
    private \$password = '{$db_pass}';
    private \$conn;

    /**
     * Get the database connection
     *
     * @return PDO
     */
    public function getConnection() {
        \$this->conn = null;

        try {
            \$this->conn = new PDO(
                \"mysql:host={\$this->host};dbname={\$this->db_name};charset=utf8mb4\",
                \$this->username,
                \$this->password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch(PDOException \$e) {
            echo \"Connection Error: \" . \$e->getMessage();
        }

        return \$this->conn;
    }
}";

if (file_put_contents('app/config/database.php', $db_config)) {
    echo "✅ Cập nhật cấu hình database\n";
} else {
    echo "❌ Không thể cập nhật file cấu hình database\n";
}

// Import database schema
if (file_exists('database.sql')) {
    echo "\n📊 Import database schema...\n";
    
    try {
        $pdo = new PDO("mysql:host={$db_host};dbname={$db_name};charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $sql = file_get_contents('database.sql');
        $pdo->exec($sql);
        echo "✅ Import database thành công\n";
        
    } catch (PDOException $e) {
        echo "❌ Lỗi import database: " . $e->getMessage() . "\n";
    }
} else {
    echo "⚠️  Không tìm thấy file database.sql\n";
}

// Tạo tài khoản admin mặc định
echo "\n👤 Tạo tài khoản admin...\n";
$admin_username = readline("Username admin (admin): ") ?: 'admin';
$admin_password = readline("Password admin (admin123): ") ?: 'admin123';
$admin_email = readline("Email admin (<EMAIL>): ") ?: '<EMAIL>';
$admin_fullname = readline("Họ tên admin (Administrator): ") ?: 'Administrator';

try {
    $pdo = new PDO("mysql:host={$db_host};dbname={$db_name};charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Tạo roles nếu chưa có
    $pdo->exec("INSERT IGNORE INTO roles (role_id, role_name) VALUES 
                (1, 'admin'), (2, 'staff'), (3, 'customer')");
    
    // Hash password
    $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
    
    // Tạo admin user
    $stmt = $pdo->prepare("INSERT INTO users (username, password, email, full_name, role_id, status) 
                          VALUES (?, ?, ?, ?, 1, 1) 
                          ON DUPLICATE KEY UPDATE 
                          password = VALUES(password), 
                          email = VALUES(email), 
                          full_name = VALUES(full_name)");
    
    $stmt->execute([$admin_username, $hashed_password, $admin_email, $admin_fullname]);
    echo "✅ Tài khoản admin đã được tạo\n";
    
} catch (PDOException $e) {
    echo "❌ Lỗi tạo tài khoản admin: " . $e->getMessage() . "\n";
}

// Cấu hình URL
echo "\n🌐 Cấu hình URL...\n";
$base_url = readline("Base URL (http://localhost/do-an): ") ?: 'http://localhost/do-an';

// Cập nhật file config
$config_content = file_get_contents('app/config/config.php');
$config_content = preg_replace(
    "/define\('BASE_URL', '[^']*'\);/", 
    "define('BASE_URL', '{$base_url}');", 
    $config_content
);

if (file_put_contents('app/config/config.php', $config_content)) {
    echo "✅ Cập nhật BASE_URL thành công\n";
} else {
    echo "❌ Không thể cập nhật BASE_URL\n";
}

// Tạo file .htaccess
$htaccess_content = "# Bật rewrite engine
RewriteEngine On

# Bảo vệ file cấu hình
<Files \"*.php\">
    Order allow,deny
    Allow from 127.0.0.1
    Allow from ::1
</Files>

# Bảo vệ thư mục app
<Directory \"app/\">
    Order deny,allow
    Deny from all
</Directory>

# Cho phép truy cập file tĩnh
<FilesMatch \"\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$\">
    Order allow,deny
    Allow from all
    ExpiresActive On
    ExpiresDefault \"access plus 1 month\"
</FilesMatch>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>";

if (file_put_contents('.htaccess', $htaccess_content)) {
    echo "✅ Tạo file .htaccess\n";
}

// Tạo file index.html cho thư mục uploads (bảo mật)
$index_html = "<!DOCTYPE html>
<html>
<head>
    <title>403 Forbidden</title>
</head>
<body>
    <h1>Directory access is forbidden.</h1>
</body>
</html>";

file_put_contents('public/uploads/index.html', $index_html);
file_put_contents('logs/index.html', $index_html);
file_put_contents('cache/index.html', $index_html);

echo "\n🎉 Cài đặt hoàn tất!\n\n";
echo "📋 Thông tin đăng nhập:\n";
echo "   URL: {$base_url}\n";
echo "   Username: {$admin_username}\n";
echo "   Password: {$admin_password}\n\n";

echo "📚 Tài liệu:\n";
echo "   - Hướng dẫn cài đặt: HUONG_DAN_CAI_DAT.md\n";
echo "   - Cấu hình chi tiết: HUONG_DAN_CAU_HINH_CHI_TIET.md\n\n";

echo "🔧 Bước tiếp theo:\n";
echo "   1. Khởi động web server (Apache/Nginx)\n";
echo "   2. Truy cập {$base_url}\n";
echo "   3. Đăng nhập với tài khoản admin\n";
echo "   4. Cấu hình thêm categories và food items\n\n";

echo "✨ Chúc bạn sử dụng thành công!\n";
?>
