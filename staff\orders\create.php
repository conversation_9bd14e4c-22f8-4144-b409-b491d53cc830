<?php
/**
 * Staff Create Order
 */
require_once '../../app/config/config.php';
require_once '../../app/config/database.php';
require_once '../../app/helpers.php';
require_once '../../app/models/Order.php';
require_once '../../app/models/Table.php';
require_once '../../app/models/FoodItem.php';
require_once '../../app/models/Category.php';

// Check if user is staff
if (!is_staff()) {
    set_error_message('Bạn không có quyền truy cập trang này');
    redirect(BASE_URL);
}

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Initialize models
$orderModel = new Order($conn);
$tableModel = new Table($conn);
$foodModel = new FoodItem($conn);
$categoryModel = new Category($conn);

// Get current user
$currentUser = get_logged_in_user();

// Get available tables
$availableTables = $tableModel->findBy('status = :status', [':status' => 'available'], 'table_number ASC');

// Get food categories and items
$categories = $categoryModel->findBy('status = :status', [':status' => 1], 'category_name ASC');
$foodItems = $foodModel->findBy('status = :status', [':status' => 1], 'food_name ASC');

// Group food items by category
$foodByCategory = [];
foreach ($foodItems as $food) {
    $foodByCategory[$food['category_id']][] = $food;
}

// Pre-selected table if provided
$selectedTableId = $_GET['table_id'] ?? '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        set_error_message('Token bảo mật không hợp lệ');
    } else {
        $tableId = (int)($_POST['table_id'] ?? 0);
        $notes = sanitize($_POST['notes'] ?? '');
        $orderItems = $_POST['items'] ?? [];

        // Validation
        $errors = [];
        
        if (!$tableId) {
            $errors[] = 'Vui lòng chọn bàn';
        } else {
            // Check if table is available
            $table = $tableModel->getById($tableId);
            if (!$table || $table['status'] !== 'available') {
                $errors[] = 'Bàn đã chọn không khả dụng';
            }
        }

        if (empty($orderItems)) {
            $errors[] = 'Vui lòng chọn ít nhất một món ăn';
        }

        // Validate order items
        $validItems = [];
        foreach ($orderItems as $item) {
            $foodId = (int)($item['food_id'] ?? 0);
            $quantity = (int)($item['quantity'] ?? 0);
            
            if ($foodId && $quantity > 0) {
                $food = $foodModel->getById($foodId);
                if ($food && $food['status'] == 1) {
                    $validItems[] = [
                        'food_id' => $foodId,
                        'quantity' => $quantity,
                        'unit_price' => $food['price'],
                        'notes' => sanitize($item['notes'] ?? '')
                    ];
                }
            }
        }

        if (empty($validItems)) {
            $errors[] = 'Không có món ăn hợp lệ nào được chọn';
        }

        if (empty($errors)) {
            try {
                // Create order with items in a single transaction
                $orderData = [
                    'table_id' => $tableId,
                    'user_id' => $currentUser['user_id'],
                    'notes' => $notes,
                    'status' => 'pending'
                ];

                $orderId = $orderModel->createOrderWithItems($orderData, $validItems);

                if ($orderId) {
                    set_success_message('Đơn hàng đã được tạo thành công');
                    redirect(STAFF_URL . '/orders/view.php?id=' . $orderId);
                } else {
                    set_error_message('Có lỗi xảy ra khi tạo đơn hàng');
                }
            } catch (Exception $e) {
                set_error_message('Có lỗi xảy ra: ' . $e->getMessage());
            }
        } else {
            foreach ($errors as $error) {
                set_error_message($error);
            }
        }
    }
}

// Set page title
$pageTitle = 'Tạo đơn hàng mới';

// Include content in layout
$content = VIEWS_DIR . '/staff/orders/create.php';
include_once VIEWS_DIR . '/layouts/main.php';
