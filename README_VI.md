# Hệ Thống Quản Lý Quán Cafe

Một hệ thống quản lý quán cafe/nhà hàng hoàn chỉnh được xây dựng bằng PHP và MySQL, hỗ trợ quản lý đơn hàng, <PERSON><PERSON> to<PERSON>, báo cáo và nhiều tính năng khác.

## 🌟 Tính Năng Chính

### 👨‍💼 Quản Trị Viên (Admin)
- **Dashboard**: Tổ<PERSON> quan doanh thu, đ<PERSON><PERSON> hàng, thống kê
- **Quản lý người dùng**: <PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON>, xóa nhân viên
- **Quản lý danh mục**: <PERSON><PERSON> loại món ăn/đồ uống
- **Quản lý món ăn**: Th<PERSON><PERSON> món mới, cập nhật giá, hình ảnh
- **Quản lý bàn**: <PERSON><PERSON><PERSON><PERSON> lậ<PERSON> s<PERSON> bà<PERSON>, s<PERSON><PERSON> chứa
- **<PERSON><PERSON><PERSON> c<PERSON>**: <PERSON><PERSON><PERSON> thu theo ng<PERSON>/tháng, m<PERSON> b<PERSON> chạ<PERSON>
- **Cài đặt hệ thống**: <PERSON><PERSON><PERSON> hình chung

### 👨‍🍳 Nhân Viên (Staff)
- **Quản lý đơn hàng**: Tạo đơn mới, cập nhật trạng thái
- **Quản lý bàn**: Xem trạng thái bàn, chuyển bàn
- **Thanh toán**: Xử lý thanh toán đơn hàng
- **In hóa đơn**: In bill cho khách hàng

### 🎯 Tính Năng Khác
- **Responsive Design**: Tương thích mobile/tablet
- **Bảo mật**: Phân quyền người dùng, mã hóa mật khẩu
- **Upload hình ảnh**: Quản lý hình ảnh món ăn
- **Tìm kiếm**: Tìm kiếm món ăn, đơn hàng
- **Xuất báo cáo**: Export dữ liệu Excel/PDF

## 🚀 Cài Đặt Nhanh

### Yêu Cầu Hệ Thống
- PHP 7.4+
- MySQL 5.7+ hoặc MariaDB 10.2+
- Apache/Nginx
- Extensions: PDO, PDO_MySQL, GD, MBString

### Cài Đặt Tự Động
```bash
# 1. Clone hoặc tải project
git clone [repository-url] do-an
cd do-an

# 2. Chạy script cài đặt
php setup.php

# 3. Làm theo hướng dẫn trên màn hình
```

### Cài Đặt Thủ Công
```bash
# 1. Tạo database
mysql -u root -p
CREATE DATABASE restaurant_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 2. Import database
mysql -u root -p restaurant_management < database.sql

# 3. Cấu hình database
# Chỉnh sửa app/config/database.php

# 4. Cấu hình URL
# Chỉnh sửa app/config/config.php

# 5. Phân quyền thư mục
chmod 755 public/uploads/
```

## 📖 Tài Liệu

- **[Hướng Dẫn Cài Đặt Chi Tiết](HUONG_DAN_CAI_DAT.md)**: Hướng dẫn cài đặt từng bước
- **[Cấu Hình Chi Tiết](HUONG_DAN_CAU_HINH_CHI_TIET.md)**: Cấu hình nâng cao, bảo mật, performance

## 🔑 Tài Khoản Mặc Định

Sau khi cài đặt, sử dụng tài khoản sau để đăng nhập:

**Admin:**
- Username: `admin`
- Password: `admin123`

**Staff:**
- Username: `staff`  
- Password: `staff123`

> ⚠️ **Lưu ý**: Đổi mật khẩu ngay sau lần đăng nhập đầu tiên!

## 🏗️ Cấu Trúc Project

```
do-an/
├── admin/                  # Giao diện quản trị
│   ├── dashboard.php
│   ├── categories/
│   ├── food/
│   ├── users/
│   └── reports/
├── staff/                  # Giao diện nhân viên
│   ├── dashboard.php
│   ├── orders/
│   └── tables/
├── app/                    # Core application
│   ├── config/            # Cấu hình
│   ├── controllers/       # Controllers
│   ├── models/           # Models
│   └── views/            # Views
├── public/               # Assets công khai
│   ├── css/
│   ├── js/
│   ├── images/
│   └── uploads/
├── database.sql          # Database schema
├── setup.php            # Script cài đặt tự động
└── README_VI.md         # Tài liệu này
```

## 🛠️ Công Nghệ Sử Dụng

- **Backend**: PHP 7.4+
- **Database**: MySQL/MariaDB
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 4
- **Libraries**: 
  - jQuery
  - Chart.js (biểu đồ)
  - DataTables (bảng dữ liệu)
  - Font Awesome (icons)

## 📱 Screenshots

### Dashboard Admin
![Admin Dashboard](public/images/screenshots/admin-dashboard.png)

### Quản Lý Đơn Hàng
![Order Management](public/images/screenshots/order-management.png)

### Giao Diện Mobile
![Mobile Interface](public/images/screenshots/mobile-interface.png)

## 🔧 Cấu Hình Nâng Cao

### Virtual Host (Apache)
```apache
<VirtualHost *:80>
    ServerName restaurant.local
    DocumentRoot "C:/xampp/htdocs/do-an"
    <Directory "C:/xampp/htdocs/do-an">
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

### SSL Configuration
```apache
<VirtualHost *:443>
    ServerName restaurant.local
    DocumentRoot "C:/xampp/htdocs/do-an"
    SSLEngine on
    SSLCertificateFile "path/to/certificate.crt"
    SSLCertificateKeyFile "path/to/private.key"
</VirtualHost>
```

## 🐛 Xử Lý Sự Cố

### Lỗi Thường Gặp

**1. Lỗi kết nối database**
```
Solution: Kiểm tra thông tin trong app/config/database.php
```

**2. Lỗi 404 Not Found**
```
Solution: Kiểm tra mod_rewrite Apache đã bật chưa
```

**3. Lỗi upload file**
```
Solution: Kiểm tra quyền ghi thư mục public/uploads/
chmod 755 public/uploads/
```

**4. Lỗi session**
```
Solution: Kiểm tra quyền ghi thư mục session của PHP
```

### Debug Mode

Để bật debug mode, chỉnh sửa `app/config/config.php`:
```php
// Bật hiển thị lỗi
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 🔒 Bảo Mật

### Checklist Bảo Mật
- [ ] Đổi mật khẩu admin mặc định
- [ ] Cập nhật thông tin database
- [ ] Bật HTTPS cho production
- [ ] Cấu hình firewall
- [ ] Backup định kỳ
- [ ] Cập nhật PHP/MySQL thường xuyên

### Backup Database
```bash
# Backup thủ công
mysqldump -u username -p restaurant_management > backup.sql

# Restore
mysql -u username -p restaurant_management < backup.sql
```

## 📈 Performance

### Tối Ưu Database
```sql
-- Tạo indexes
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_orders_status ON orders(status);

-- Optimize tables
OPTIMIZE TABLE orders;
OPTIMIZE TABLE order_details;
```

### Caching
- Bật OPcache cho PHP
- Sử dụng browser caching
- Compress CSS/JS files

## 🤝 Đóng Góp

Chúng tôi hoan nghênh mọi đóng góp! Vui lòng:

1. Fork project
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Project này được phân phối dưới MIT License. Xem file `LICENSE` để biết thêm chi tiết.

## 📞 Hỗ Trợ

Nếu gặp vấn đề hoặc cần hỗ trợ:

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 Wiki: [Project Wiki](https://github.com/your-repo/wiki)

## 🙏 Cảm Ơn

Cảm ơn tất cả những người đã đóng góp vào project này!

---

**Made with ❤️ in Vietnam**
