<?php
/**
 * Real-time Dashboard API
 * Provides AJAX endpoints for live dashboard updates
 */
require_once '../../app/config/config.php';
require_once '../../app/config/database.php';
require_once '../../app/helpers.php';
require_once '../../app/models/Order.php';
require_once '../../app/models/Table.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is authenticated
if (!is_authenticated()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Check if user is staff or admin
if (!is_staff() && !is_admin()) {
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit;
}

// Get request method and action
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Initialize models
$orderModel = new Order($conn);
$tableModel = new Table($conn);

// Get current user
$currentUser = get_logged_in_user();

try {
    switch ($action) {
        case 'active_orders':
            handleActiveOrders($orderModel, $tableModel, $currentUser);
            break;
            
        case 'table_status':
            handleTableStatus($tableModel);
            break;
            
        case 'kitchen_queue':
            handleKitchenQueue($orderModel, $tableModel);
            break;
            
        case 'order_stats':
            handleOrderStats($orderModel, $currentUser);
            break;
            
        case 'update_order_status':
            if ($method === 'POST') {
                handleUpdateOrderStatus($orderModel, $currentUser);
            } else {
                http_response_code(405);
                echo json_encode(['error' => 'Method not allowed']);
            }
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}

/**
 * Get active orders for current user
 */
function handleActiveOrders($orderModel, $tableModel, $currentUser) {
    $activeOrders = $orderModel->getActiveOrdersByUser($currentUser['user_id']);
    
    $ordersData = [];
    foreach ($activeOrders as $order) {
        $table = $tableModel->getById($order['table_id']);
        
        $ordersData[] = [
            'order_id' => $order['order_id'],
            'table_number' => $table['table_number'],
            'total_amount' => $order['final_amount'],
            'status' => $order['status'],
            'status_text' => getStatusText($order['status']),
            'status_class' => getStatusClass($order['status']),
            'order_time' => date('H:i', strtotime($order['order_date'])),
            'elapsed_time' => getElapsedTime($order['order_date']),
            'formatted_amount' => format_currency($order['final_amount'])
        ];
    }
    
    echo json_encode([
        'success' => true,
        'orders' => $ordersData,
        'count' => count($ordersData),
        'timestamp' => time()
    ]);
}

/**
 * Get table status counts
 */
function handleTableStatus($tableModel) {
    $statusCounts = $tableModel->getStatusCounts();
    $allTables = $tableModel->getAllTablesWithOrders();
    
    $tablesData = [];
    foreach ($allTables as $table) {
        $tablesData[] = [
            'table_id' => $table['table_id'],
            'table_number' => $table['table_number'],
            'status' => $table['status'],
            'status_text' => getTableStatusText($table['status']),
            'status_class' => getTableStatusClass($table['status']),
            'order_id' => $table['order_id'],
            'order_status' => $table['order_status'],
            'total_amount' => $table['total_amount'] ? format_currency($table['total_amount']) : null
        ];
    }
    
    echo json_encode([
        'success' => true,
        'status_counts' => $statusCounts,
        'tables' => $tablesData,
        'timestamp' => time()
    ]);
}

/**
 * Get kitchen queue (all active orders)
 */
function handleKitchenQueue($orderModel, $tableModel) {
    $activeOrders = $orderModel->getActiveOrders();
    
    $queueData = [];
    foreach ($activeOrders as $order) {
        $table = $tableModel->getById($order['table_id']);
        $orderDetails = $orderModel->getOrderDetails($order['order_id']);
        
        $queueData[] = [
            'order_id' => $order['order_id'],
            'table_number' => $table['table_number'],
            'status' => $order['status'],
            'status_text' => getStatusText($order['status']),
            'status_class' => getStatusClass($order['status']),
            'order_time' => date('H:i d/m', strtotime($order['order_date'])),
            'elapsed_time' => getElapsedTime($order['order_date']),
            'priority' => getPriority($order['order_date']),
            'items_count' => count($orderDetails),
            'total_amount' => format_currency($order['final_amount'])
        ];
    }
    
    // Sort by priority (oldest first)
    usort($queueData, function($a, $b) {
        return $b['priority'] - $a['priority'];
    });
    
    echo json_encode([
        'success' => true,
        'queue' => $queueData,
        'count' => count($queueData),
        'timestamp' => time()
    ]);
}

/**
 * Get order statistics for current user
 */
function handleOrderStats($orderModel, $currentUser) {
    $today = date('Y-m-d');
    
    // Get today's orders count
    $todayOrders = $orderModel->findBy(
        'user_id = :user_id AND DATE(order_date) = :today',
        [':user_id' => $currentUser['user_id'], ':today' => $today]
    );
    
    // Get today's revenue
    $todayRevenue = $orderModel->query(
        "SELECT SUM(p.payment_amount) as total_revenue, COUNT(p.payment_id) as completed_orders 
         FROM payments p 
         JOIN orders o ON p.order_id = o.order_id 
         WHERE o.user_id = :user_id AND DATE(p.payment_date) = :today AND p.payment_status = 'completed'",
        [':user_id' => $currentUser['user_id'], ':today' => $today]
    );
    
    $revenue = $todayRevenue[0] ?? ['total_revenue' => 0, 'completed_orders' => 0];
    
    echo json_encode([
        'success' => true,
        'stats' => [
            'total_orders' => count($todayOrders),
            'completed_orders' => (int)$revenue['completed_orders'],
            'pending_orders' => count($todayOrders) - (int)$revenue['completed_orders'],
            'total_revenue' => format_currency($revenue['total_revenue'] ?? 0),
            'raw_revenue' => $revenue['total_revenue'] ?? 0
        ],
        'timestamp' => time()
    ]);
}

/**
 * Update order status
 */
function handleUpdateOrderStatus($orderModel, $currentUser) {
    // Verify CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid CSRF token']);
        return;
    }
    
    $orderId = (int)($_POST['order_id'] ?? 0);
    $newStatus = sanitize($_POST['status'] ?? '');
    
    if (!$orderId || !in_array($newStatus, ['pending', 'processing', 'completed', 'cancelled'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid parameters']);
        return;
    }
    
    // Check if user owns this order
    $order = $orderModel->getById($orderId);
    if (!$order || $order['user_id'] != $currentUser['user_id']) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    // Update order status
    $success = $orderModel->update($orderId, ['status' => $newStatus]);
    
    if ($success) {
        echo json_encode([
            'success' => true,
            'message' => 'Trạng thái đơn hàng đã được cập nhật',
            'new_status' => $newStatus,
            'status_text' => getStatusText($newStatus),
            'status_class' => getStatusClass($newStatus)
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update order status']);
    }
}

/**
 * Helper functions
 */
function getStatusText($status) {
    switch ($status) {
        case 'pending': return 'Đang chờ';
        case 'processing': return 'Đang xử lý';
        case 'completed': return 'Hoàn thành';
        case 'cancelled': return 'Đã hủy';
        default: return 'Không xác định';
    }
}

function getStatusClass($status) {
    switch ($status) {
        case 'pending': return 'bg-warning';
        case 'processing': return 'bg-info';
        case 'completed': return 'bg-success';
        case 'cancelled': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

function getTableStatusText($status) {
    switch ($status) {
        case 'available': return 'Trống';
        case 'occupied': return 'Có khách';
        case 'reserved': return 'Đã đặt';
        case 'maintenance': return 'Bảo trì';
        default: return 'Không xác định';
    }
}

function getTableStatusClass($status) {
    switch ($status) {
        case 'available': return 'bg-success';
        case 'occupied': return 'bg-danger';
        case 'reserved': return 'bg-warning';
        case 'maintenance': return 'bg-secondary';
        default: return 'bg-light';
    }
}

function getElapsedTime($orderDate) {
    $now = new DateTime();
    $orderTime = new DateTime($orderDate);
    $diff = $now->diff($orderTime);
    
    if ($diff->h > 0) {
        return $diff->h . 'h ' . $diff->i . 'm';
    } else {
        return $diff->i . 'm';
    }
}

function getPriority($orderDate) {
    $now = new DateTime();
    $orderTime = new DateTime($orderDate);
    return $now->getTimestamp() - $orderTime->getTimestamp();
}
?>
