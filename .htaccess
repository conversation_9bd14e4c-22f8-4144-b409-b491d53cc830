# Bật rewrite engine
RewriteEngine On

# Bảo vệ file c<PERSON>u hình
<Files "*.php">
    Order allow,deny
    Allow from 127.0.0.1
    Allow from ::1
</Files>

# B<PERSON><PERSON> vệ thư mục app
<Directory "app/">
    Order deny,allow
    Deny from all
</Directory>

# Cho phép truy cập file tĩnh
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
    Order allow,deny
    Allow from all
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>