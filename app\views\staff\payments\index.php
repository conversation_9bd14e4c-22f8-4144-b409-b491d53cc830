<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="mb-0"><PERSON><PERSON><PERSON><PERSON> lý thanh toán</h1>
            <p class="text-muted"><PERSON><PERSON> lý thanh toán cho các đơn hàng đã hoàn thành</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4 class="mb-0"><?= format_currency($todayRevenue) ?></h4>
                    <small>Doanh thu hôm nay</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Orders Awaiting Payment -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Đơn hàng chờ thanh toán</h5>
                </div>
                <div class="card-body">
                    <?php 
                    $unpaidOrders = array_filter($completedOrders, function($order) {
                        return empty($order['payment_id']) || $order['payment_status'] !== 'completed';
                    });
                    ?>
                    
                    <?php if (empty($unpaidOrders)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-muted">Tất cả đơn hàng đã được thanh toán</h5>
                            <p class="text-muted">Không có đơn hàng nào đang chờ thanh toán.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Đơn hàng</th>
                                        <th>Bàn</th>
                                        <th>Thời gian</th>
                                        <th>Tổng tiền</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($unpaidOrders as $order): ?>
                                        <tr>
                                            <td>
                                                <span class="fw-bold">#<?= $order['order_id'] ?></span>
                                            </td>
                                            <td><?= $order['table_number'] ?></td>
                                            <td><?= date('d/m/Y H:i', strtotime($order['order_date'])) ?></td>
                                            <td class="fw-bold text-success"><?= format_currency($order['final_amount']) ?></td>
                                            <td>
                                                <?php if (empty($order['payment_id'])): ?>
                                                    <span class="badge bg-warning">Chưa thanh toán</span>
                                                <?php elseif ($order['payment_status'] === 'pending'): ?>
                                                    <span class="badge bg-info">Đang xử lý</span>
                                                <?php elseif ($order['payment_status'] === 'failed'): ?>
                                                    <span class="badge bg-danger">Thất bại</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= STAFF_URL ?>/orders/view.php?id=<?= $order['order_id'] ?>" 
                                                       class="btn btn-outline-primary" title="Xem chi tiết">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= STAFF_URL ?>/payments/create.php?order_id=<?= $order['order_id'] ?>" 
                                                       class="btn btn-success" title="Thanh toán">
                                                        <i class="fas fa-credit-card"></i> Thanh toán
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Today's Payments -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Thanh toán hôm nay</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($todayPayments)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-receipt fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Chưa có thanh toán nào hôm nay</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach (array_slice($todayPayments, 0, 10) as $payment): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="fw-bold">Đơn #<?= $payment['order_id'] ?></div>
                                            <small class="text-muted">
                                                Bàn <?= $payment['table_number'] ?> • 
                                                <?= date('H:i', strtotime($payment['payment_date'])) ?>
                                            </small>
                                            <div class="mt-1">
                                                <?php
                                                $methodText = '';
                                                switch ($payment['payment_method']) {
                                                    case 'cash':
                                                        $methodText = 'Tiền mặt';
                                                        break;
                                                    case 'credit_card':
                                                        $methodText = 'Thẻ tín dụng';
                                                        break;
                                                    case 'debit_card':
                                                        $methodText = 'Thẻ ghi nợ';
                                                        break;
                                                    case 'mobile_payment':
                                                        $methodText = 'Thanh toán di động';
                                                        break;
                                                    default:
                                                        $methodText = 'Khác';
                                                        break;
                                                }
                                                ?>
                                                <small class="badge bg-secondary"><?= $methodText ?></small>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold text-success">
                                                <?= format_currency($payment['payment_amount']) ?>
                                            </div>
                                            <?php
                                            $statusClass = '';
                                            $statusText = '';
                                            
                                            switch ($payment['payment_status']) {
                                                case 'completed':
                                                    $statusClass = 'bg-success';
                                                    $statusText = 'Thành công';
                                                    break;
                                                case 'pending':
                                                    $statusClass = 'bg-warning';
                                                    $statusText = 'Đang xử lý';
                                                    break;
                                                case 'failed':
                                                    $statusClass = 'bg-danger';
                                                    $statusText = 'Thất bại';
                                                    break;
                                                case 'refunded':
                                                    $statusClass = 'bg-info';
                                                    $statusText = 'Đã hoàn';
                                                    break;
                                                default:
                                                    $statusClass = 'bg-secondary';
                                                    $statusText = 'Không xác định';
                                                    break;
                                            }
                                            ?>
                                            <small class="badge <?= $statusClass ?>"><?= $statusText ?></small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if (count($todayPayments) > 10): ?>
                            <div class="text-center mt-3">
                                <small class="text-muted">Và <?= count($todayPayments) - 10 ?> thanh toán khác...</small>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                <div class="card-footer bg-light text-center">
                    <strong>Tổng: <?= format_currency($todayRevenue) ?></strong>
                    <br>
                    <small class="text-muted"><?= count($todayPayments) ?> giao dịch</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Thống kê thanh toán hôm nay</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <?php
                        $paymentMethods = ['cash' => 'Tiền mặt', 'credit_card' => 'Thẻ tín dụng', 'debit_card' => 'Thẻ ghi nợ', 'mobile_payment' => 'Thanh toán di động'];
                        $methodStats = [];
                        
                        foreach ($paymentMethods as $method => $label) {
                            $methodPayments = array_filter($todayPayments, function($p) use ($method) {
                                return $p['payment_method'] === $method && $p['payment_status'] === 'completed';
                            });
                            
                            $methodStats[$method] = [
                                'label' => $label,
                                'count' => count($methodPayments),
                                'amount' => array_sum(array_column($methodPayments, 'payment_amount'))
                            ];
                        }
                        ?>
                        
                        <?php foreach ($methodStats as $method => $stats): ?>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-primary"><?= $stats['count'] ?></h4>
                                    <p class="mb-1"><?= $stats['label'] ?></p>
                                    <small class="text-muted"><?= format_currency($stats['amount']) ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
