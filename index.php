<?php
/**
 * Main entry point for the application
 */

// Load configuration
require_once 'app/config/config.php';
require_once 'app/helpers.php';

// Check if user is logged in
if (is_authenticated()) {
    // Redirect based on role
    if (is_admin()) {
        redirect(ADMIN_URL . '/dashboard.php');
    } else if (is_staff()) {
        redirect(STAFF_URL . '/dashboard.php');
    } else {
        // Unknown role, logout for security
        session_destroy();
        redirect(BASE_URL . '/login.php');
    }
} else {
    // Redirect to login page
    redirect(BASE_URL . '/login.php');
}